// Contract addresses - these will be updated after deployment
export const OTC_HUB_ADDRESS = (import.meta.env.VITE_OTC_HUB_CONTRACT_ADDRESS || 
  '0x5FbDB2315678afecb367f032d93F642f64180aa3') as `0x${string}`

// OTC Hub Contract ABI (simplified for key functions)
export const OTC_HUB_ABI = [
  {
    "type": "function",
    "name": "createTrade",
    "inputs": [
      {"name": "maker", "type": "address"},
      {"name": "depositToken", "type": "address"},
      {"name": "price", "type": "uint256"},
      {"name": "depositAmount", "type": "uint256"},
      {"name": "fundingWindow", "type": "uint256"},
      {"name": "direction", "type": "uint8"},
      {"name": "agreementHash", "type": "bytes32"}
    ],
    "outputs": [{"name": "tradeId", "type": "uint256"}],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "fundTrade",
    "inputs": [{"name": "tradeId", "type": "uint256"}],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "confirmTrade",
    "inputs": [{"name": "tradeId", "type": "uint256"}],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "disputeTrade",
    "inputs": [{"name": "tradeId", "type": "uint256"}],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "cancelTrade",
    "inputs": [{"name": "tradeId", "type": "uint256"}],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "getTrade",
    "inputs": [{"name": "tradeId", "type": "uint256"}],
    "outputs": [
      {
        "type": "tuple",
        "components": [
          {"name": "maker", "type": "address"},
          {"name": "taker", "type": "address"},
          {"name": "depositToken", "type": "address"},
          {"name": "price", "type": "uint256"},
          {"name": "depositAmount", "type": "uint256"},
          {"name": "fundingDeadline", "type": "uint256"},
          {"name": "direction", "type": "uint8"},
          {"name": "agreementHash", "type": "bytes32"},
          {"name": "status", "type": "uint8"},
          {"name": "makerConfirmed", "type": "bool"},
          {"name": "takerConfirmed", "type": "bool"}
        ]
      }
    ],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "getTradeCount",
    "inputs": [],
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view"
  },
  {
    "type": "event",
    "name": "TradeCreated",
    "inputs": [
      {"name": "tradeId", "type": "uint256", "indexed": true},
      {"name": "maker", "type": "address", "indexed": true},
      {"name": "depositToken", "type": "address", "indexed": true},
      {"name": "price", "type": "uint256", "indexed": false},
      {"name": "depositAmount", "type": "uint256", "indexed": false},
      {"name": "direction", "type": "uint8", "indexed": false}
    ]
  },
  {
    "type": "event",
    "name": "TradeFunded",
    "inputs": [
      {"name": "tradeId", "type": "uint256", "indexed": true},
      {"name": "funder", "type": "address", "indexed": true},
      {"name": "amount", "type": "uint256", "indexed": false}
    ]
  },
  {
    "type": "event",
    "name": "TradeConfirmed",
    "inputs": [
      {"name": "tradeId", "type": "uint256", "indexed": true},
      {"name": "confirmer", "type": "address", "indexed": true}
    ]
  },
  {
    "type": "event",
    "name": "TradeSettled",
    "inputs": [
      {"name": "tradeId", "type": "uint256", "indexed": true},
      {"name": "maker", "type": "address", "indexed": true},
      {"name": "taker", "type": "address", "indexed": true}
    ]
  },
  {
    "type": "event",
    "name": "TradeCancelled",
    "inputs": [
      {"name": "tradeId", "type": "uint256", "indexed": true},
      {"name": "canceller", "type": "address", "indexed": true}
    ]
  },
  {
    "type": "event",
    "name": "TradeDisputed",
    "inputs": [
      {"name": "tradeId", "type": "uint256", "indexed": true},
      {"name": "disputer", "type": "address", "indexed": true}
    ]
  }
] as const

// Standard ERC20 ABI (for token approvals and transfers)
export const ERC20_ABI = [
  {
    "type": "function",
    "name": "approve",
    "inputs": [
      {"name": "spender", "type": "address"},
      {"name": "amount", "type": "uint256"}
    ],
    "outputs": [{"name": "", "type": "bool"}],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "allowance",
    "inputs": [
      {"name": "owner", "type": "address"},
      {"name": "spender", "type": "address"}
    ],
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "balanceOf",
    "inputs": [{"name": "account", "type": "address"}],
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "transfer",
    "inputs": [
      {"name": "to", "type": "address"},
      {"name": "amount", "type": "uint256"}
    ],
    "outputs": [{"name": "", "type": "bool"}],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "transferFrom",
    "inputs": [
      {"name": "from", "type": "address"},
      {"name": "to", "type": "address"},
      {"name": "amount", "type": "uint256"}
    ],
    "outputs": [{"name": "", "type": "bool"}],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "decimals",
    "inputs": [],
    "outputs": [{"name": "", "type": "uint8"}],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "symbol",
    "inputs": [],
    "outputs": [{"name": "", "type": "string"}],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "name",
    "inputs": [],
    "outputs": [{"name": "", "type": "string"}],
    "stateMutability": "view"
  }
] as const

// Chain configuration
export const SUPPORTED_CHAINS = {
  localhost: {
    id: 31337,
    name: 'Localhost',
    rpcUrl: 'http://127.0.0.1:8545',
    blockExplorer: null
  },
  sepolia: {
    id: 11155111,
    name: 'Sepolia',
    rpcUrl: 'https://sepolia.infura.io/v3/',
    blockExplorer: 'https://sepolia.etherscan.io'
  }
} as const
