{"name": "otchub-backend", "version": "1.0.0", "description": "OTC Hub Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["otc", "blockchain", "defi", "api"], "author": "0xluoye", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "viem": "^2.7.0", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "redis": "^4.6.12", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}