import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useAccount } from 'wagmi'
import { 
  ArrowLeftIcon,
  UserIcon,
  ClockIcon,
  DollarSignIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertTriangleIcon,
  ExternalLinkIcon
} from 'lucide-react'

import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { useFundTrade } from '@/hooks/useFundTrade'
import { useConfirmTrade } from '@/hooks/useConfirmTrade'
import { useDisputeTrade } from '@/hooks/useDisputeTrade'
import { TradeStatus, TradeDirection } from '@/types'
import { formatToken, formatDate, truncateAddress } from '@/lib/utils'
import { cn } from '@/lib/utils'

const STATUS_CONFIG = {
  [TradeStatus.Open]: {
    label: 'Open',
    icon: ClockIcon,
    color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    description: 'Waiting for participants to deposit collateral'
  },
  [TradeStatus.Funded]: {
    label: 'Funded',
    icon: CheckCircleIcon,
    color: 'text-blue-600 bg-blue-50 border-blue-200',
    description: 'Both parties have deposited, trade can proceed'
  },
  [TradeStatus.Settled]: {
    label: 'Settled',
    icon: CheckCircleIcon,
    color: 'text-green-600 bg-green-50 border-green-200',
    description: 'Trade completed successfully'
  },
  [TradeStatus.Cancelled]: {
    label: 'Cancelled',
    icon: XCircleIcon,
    color: 'text-gray-600 bg-gray-50 border-gray-200',
    description: 'Trade was cancelled'
  },
  [TradeStatus.Disputed]: {
    label: 'Disputed',
    icon: AlertTriangleIcon,
    color: 'text-red-600 bg-red-50 border-red-200',
    description: 'Trade is under dispute resolution'
  },
  [TradeStatus.AdminClosed]: {
    label: 'Admin Closed',
    icon: XCircleIcon,
    color: 'text-purple-600 bg-purple-50 border-purple-200',
    description: 'Trade was closed by administrator'
  }
}

const DIRECTION_LABELS = {
  [TradeDirection.MakerSells]: 'Maker Selling',
  [TradeDirection.MakerBuys]: 'Maker Buying'
}

export default function TradeDetailPage() {
  const { tradeId } = useParams<{ tradeId: string }>()
  const navigate = useNavigate()
  const { address } = useAccount()
  const { isAuthenticated } = useAuth()

  const { data: trade, isLoading, error } = useQuery({
    queryKey: ['trade', tradeId],
    queryFn: () => api.trades.getTrade(tradeId!),
    enabled: !!tradeId
  })

  const { 
    fundTrade, 
    isLoading: isFunding, 
    error: fundError,
    needsApproval,
    approve,
    isApproving
  } = useFundTrade()

  const { 
    confirmTrade, 
    isLoading: isConfirming, 
    error: confirmError 
  } = useConfirmTrade()

  const { 
    disputeTrade, 
    isLoading: isDisputing, 
    error: disputeError 
  } = useDisputeTrade()

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !trade) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="text-red-600 mb-4">Failed to load trade</div>
        <button onClick={() => navigate('/trades')} className="btn-primary">
          Back to Trades
        </button>
      </div>
    )
  }

  const statusConfig = STATUS_CONFIG[trade.status]
  const StatusIcon = statusConfig.icon
  const isMaker = address && trade.maker_address.toLowerCase() === address.toLowerCase()
  const isTaker = address && trade.taker_address?.toLowerCase() === address.toLowerCase()
  const isParticipant = isMaker || isTaker

  const canFund = isAuthenticated && isParticipant && trade.status === TradeStatus.Open
  const canConfirm = isAuthenticated && isParticipant && trade.status === TradeStatus.Funded
  const canDispute = isAuthenticated && isParticipant && trade.status === TradeStatus.Funded

  const handleFund = async () => {
    if (!trade) return
    
    try {
      await fundTrade({
        tradeId: trade.trade_id,
        tokenAddress: trade.deposit_token_address,
        amount: trade.deposit_amount
      })
    } catch (err) {
      console.error('Failed to fund trade:', err)
    }
  }

  const handleConfirm = async () => {
    if (!trade) return
    
    try {
      await confirmTrade({ tradeId: trade.trade_id })
    } catch (err) {
      console.error('Failed to confirm trade:', err)
    }
  }

  const handleDispute = async () => {
    if (!trade) return
    
    try {
      await disputeTrade({ tradeId: trade.trade_id })
    } catch (err) {
      console.error('Failed to dispute trade:', err)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/trades')}
          className="btn-secondary btn-sm"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back to Trades
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trade #{trade.trade_id}</h1>
          <p className="text-gray-600">Trade details and actions</p>
        </div>
      </div>

      {/* Trade Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center gap-3 mb-4">
          <div className={cn(
            'flex items-center gap-2 px-3 py-2 rounded-full border',
            statusConfig.color
          )}>
            <StatusIcon className="w-5 h-5" />
            <span className="font-medium">{statusConfig.label}</span>
          </div>
          <span className="text-sm text-gray-500">{statusConfig.description}</span>
        </div>

        {/* Trade Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500">Direction</div>
            <div className="font-medium">{DIRECTION_LABELS[trade.direction]}</div>
          </div>
          
          <div className="flex items-center gap-3">
            <DollarSignIcon className="w-4 h-4 text-gray-400" />
            <div>
              <div className="text-sm text-gray-500">Price</div>
              <div className="font-semibold">${formatToken(trade.price, 2)}</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <ShieldCheckIcon className="w-4 h-4 text-gray-400" />
            <div>
              <div className="text-sm text-gray-500">Amount</div>
              <div className="font-semibold">
                {formatToken(trade.deposit_amount)} {trade.deposit_token_symbol}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <ClockIcon className="w-4 h-4 text-gray-400" />
            <div>
              <div className="text-sm text-gray-500">Created</div>
              <div className="font-semibold">{formatDate(trade.created_at)}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Participants */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Participants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center gap-3">
            <UserIcon className="w-5 h-5 text-gray-400" />
            <div className="flex-1">
              <div className="text-sm text-gray-500">Maker</div>
              <div className="font-mono text-sm">{trade.maker_address}</div>
              {isMaker && <span className="text-xs text-blue-600">(You)</span>}
            </div>
            <a
              href={`https://etherscan.io/address/${trade.maker_address}`}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-secondary btn-sm"
            >
              <ExternalLinkIcon className="w-4 h-4" />
            </a>
          </div>
          
          <div className="flex items-center gap-3">
            <UserIcon className="w-5 h-5 text-gray-400" />
            <div className="flex-1">
              <div className="text-sm text-gray-500">Taker</div>
              <div className="font-mono text-sm">
                {trade.taker_address || 'Not assigned'}
              </div>
              {isTaker && <span className="text-xs text-blue-600">(You)</span>}
            </div>
            {trade.taker_address && (
              <a
                href={`https://etherscan.io/address/${trade.taker_address}`}
                target="_blank"
                rel="noopener noreferrer"
                className="btn-secondary btn-sm"
              >
                <ExternalLinkIcon className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>
      </div>

      {/* Timeline */}
      {trade.funding_deadline && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Trade created: {formatDate(trade.created_at)}</span>
            </div>
            <div className="flex items-center gap-3">
              <div className={cn(
                'w-2 h-2 rounded-full',
                trade.status === TradeStatus.Open ? 'bg-yellow-500' : 'bg-green-500'
              )}></div>
              <span className="text-sm">
                Funding deadline: {formatDate(trade.funding_deadline)}
                {new Date(trade.funding_deadline) < new Date() && (
                  <span className="text-red-600 ml-2">(Expired)</span>
                )}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      {isParticipant && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
          
          <div className="space-y-4">
            {/* Funding Actions */}
            {canFund && (
              <div className="space-y-2">
                {needsApproval && (
                  <button
                    onClick={approve}
                    disabled={isApproving}
                    className="btn-secondary w-full"
                  >
                    {isApproving ? 'Approving...' : 'Approve Token'}
                  </button>
                )}
                <button
                  onClick={handleFund}
                  disabled={isFunding || needsApproval}
                  className="btn-primary w-full"
                >
                  {isFunding ? 'Funding...' : 'Fund Trade'}
                </button>
                {fundError && (
                  <p className="text-red-600 text-sm">{fundError}</p>
                )}
              </div>
            )}

            {/* Confirmation Actions */}
            {canConfirm && (
              <div className="flex gap-3">
                <button
                  onClick={handleConfirm}
                  disabled={isConfirming}
                  className="btn-primary flex-1"
                >
                  {isConfirming ? 'Confirming...' : 'Confirm Completion'}
                </button>
                <button
                  onClick={handleDispute}
                  disabled={isDisputing}
                  className="btn-secondary flex-1"
                >
                  {isDisputing ? 'Disputing...' : 'Dispute Trade'}
                </button>
              </div>
            )}

            {/* Error Messages */}
            {confirmError && (
              <p className="text-red-600 text-sm">{confirmError}</p>
            )}
            {disputeError && (
              <p className="text-red-600 text-sm">{disputeError}</p>
            )}
          </div>
        </div>
      )}

      {/* Agreement Hash */}
      {trade.agreement_hash && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Agreement</h2>
          <div>
            <div className="text-sm text-gray-500">Agreement Hash</div>
            <div className="font-mono text-sm break-all">{trade.agreement_hash}</div>
          </div>
        </div>
      )}

      {/* Transaction Hash */}
      {trade.transaction_hash && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Blockchain</h2>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Transaction Hash</div>
              <div className="font-mono text-sm">{truncateAddress(trade.transaction_hash)}</div>
            </div>
            <a
              href={`https://etherscan.io/tx/${trade.transaction_hash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-secondary btn-sm"
            >
              <ExternalLinkIcon className="w-4 h-4 mr-1" />
              View on Etherscan
            </a>
          </div>
        </div>
      )}
    </div>
  )
}
