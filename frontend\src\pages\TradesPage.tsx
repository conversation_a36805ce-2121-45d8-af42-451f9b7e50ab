import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  FilterIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertTriangleIcon,
  ExternalLinkIcon,
  UserIcon
} from 'lucide-react'

import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { Trade, TradeStatus, TradeDirection } from '@/types'
import { formatToken, formatDate, truncateAddress } from '@/lib/utils'
import { cn } from '@/lib/utils'

const STATUS_CONFIG = {
  [TradeStatus.Open]: {
    label: 'Open',
    icon: ClockIcon,
    color: 'text-yellow-600 bg-yellow-50 border-yellow-200'
  },
  [TradeStatus.Funded]: {
    label: 'Funded',
    icon: CheckCircleIcon,
    color: 'text-blue-600 bg-blue-50 border-blue-200'
  },
  [TradeStatus.Settled]: {
    label: 'Settled',
    icon: CheckCircleIcon,
    color: 'text-green-600 bg-green-50 border-green-200'
  },
  [TradeStatus.Cancelled]: {
    label: 'Cancelled',
    icon: XCircleIcon,
    color: 'text-gray-600 bg-gray-50 border-gray-200'
  },
  [TradeStatus.Disputed]: {
    label: 'Disputed',
    icon: AlertTriangleIcon,
    color: 'text-red-600 bg-red-50 border-red-200'
  },
  [TradeStatus.AdminClosed]: {
    label: 'Admin Closed',
    icon: XCircleIcon,
    color: 'text-purple-600 bg-purple-50 border-purple-200'
  }
}

const DIRECTION_LABELS = {
  [TradeDirection.MakerSells]: 'Selling',
  [TradeDirection.MakerBuys]: 'Buying'
}

export default function TradesPage() {
  const { isAuthenticated } = useAuth()
  const [statusFilter, setStatusFilter] = useState<TradeStatus | 'all'>('all')

  const { data: trades, isLoading, error } = useQuery({
    queryKey: ['trades', { status: statusFilter === 'all' ? undefined : statusFilter }],
    queryFn: () => api.trades.getUserTrades({
      status: statusFilter === 'all' ? undefined : statusFilter
    }),
    enabled: isAuthenticated
  })

  const { data: stats } = useQuery({
    queryKey: ['trade-stats'],
    queryFn: api.trades.getUserStats,
    enabled: isAuthenticated
  })

  if (!isAuthenticated) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">
          Please connect and authenticate your wallet to view your trades
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load trades</div>
        <button 
          onClick={() => window.location.reload()} 
          className="btn-primary"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">My Trades</h1>
        <p className="text-gray-600">Track your OTC trading activity</p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="text-sm text-gray-500">Total Trades</div>
            <div className="text-2xl font-bold text-gray-900">{stats.total_trades}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="text-sm text-gray-500">Completed</div>
            <div className="text-2xl font-bold text-green-600">{stats.completed_trades}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="text-sm text-gray-500">Total Volume</div>
            <div className="text-2xl font-bold text-gray-900">
              ${formatToken(stats.total_volume, 2)}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="text-sm text-gray-500">Success Rate</div>
            <div className="text-2xl font-bold text-blue-600">
              {stats.total_trades > 0 
                ? Math.round((stats.completed_trades / stats.total_trades) * 100)
                : 0}%
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <FilterIcon className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-500">Filter by status:</span>
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as TradeStatus | 'all')}
            className="input w-auto"
          >
            <option value="all">All Statuses</option>
            {Object.entries(STATUS_CONFIG).map(([status, config]) => (
              <option key={status} value={status}>
                {config.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Trades List */}
      {isLoading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow-sm border animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      ) : trades?.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm border">
          <div className="text-gray-500 mb-4">No trades found</div>
          <p className="text-sm text-gray-400">
            {statusFilter === 'all' 
              ? "You haven't participated in any trades yet"
              : `No trades with status "${STATUS_CONFIG[statusFilter as TradeStatus]?.label}"`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {trades?.map((trade) => (
            <TradeCard key={trade.trade_id} trade={trade} />
          ))}
        </div>
      )}
    </div>
  )
}

interface TradeCardProps {
  trade: Trade
}

function TradeCard({ trade }: TradeCardProps) {
  const statusConfig = STATUS_CONFIG[trade.status]
  const StatusIcon = statusConfig.icon

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Left Section */}
        <div className="flex-1 space-y-3">
          {/* Header */}
          <div className="flex items-center gap-3">
            <div className={cn(
              'flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium',
              statusConfig.color
            )}>
              <StatusIcon className="w-4 h-4" />
              {statusConfig.label}
            </div>
            <span className="text-sm text-gray-500">
              Trade #{trade.trade_id}
            </span>
          </div>

          {/* Trade Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="text-sm text-gray-500">Direction</div>
              <div className="font-medium">
                {DIRECTION_LABELS[trade.direction]}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Amount</div>
              <div className="font-medium">
                {formatToken(trade.deposit_amount)} {trade.deposit_token_symbol}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Price</div>
              <div className="font-medium">
                ${formatToken(trade.price, 2)}
              </div>
            </div>
          </div>

          {/* Participants */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <UserIcon className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-sm text-gray-500">Maker</div>
                <div className="text-sm font-mono">
                  {truncateAddress(trade.maker_address)}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <UserIcon className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-sm text-gray-500">Taker</div>
                <div className="text-sm font-mono">
                  {trade.taker_address ? truncateAddress(trade.taker_address) : 'Not assigned'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex flex-col items-end gap-3">
          {/* Timestamps */}
          <div className="text-right">
            <div className="text-sm text-gray-500">Created</div>
            <div className="text-sm">{formatDate(trade.created_at)}</div>
            {trade.funding_deadline && (
              <>
                <div className="text-sm text-gray-500 mt-2">Funding Deadline</div>
                <div className="text-sm">{formatDate(trade.funding_deadline)}</div>
              </>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            {trade.transaction_hash && (
              <a
                href={`https://etherscan.io/tx/${trade.transaction_hash}`}
                target="_blank"
                rel="noopener noreferrer"
                className="btn-secondary btn-sm"
              >
                <ExternalLinkIcon className="w-4 h-4 mr-1" />
                View Tx
              </a>
            )}
            
            {/* Trade-specific actions based on status */}
            {trade.status === TradeStatus.Open && (
              <button className="btn-primary btn-sm">
                Fund Trade
              </button>
            )}
            
            {trade.status === TradeStatus.Funded && (
              <button className="btn-primary btn-sm">
                Confirm Completion
              </button>
            )}
            
            {trade.status === TradeStatus.Funded && (
              <button className="btn-secondary btn-sm">
                Dispute
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Agreement Hash */}
      {trade.agreement_hash && (
        <div className="mt-4 pt-4 border-t">
          <div className="text-sm text-gray-500">Agreement Hash</div>
          <div className="text-sm font-mono text-gray-700 break-all">
            {trade.agreement_hash}
          </div>
        </div>
      )}
    </div>
  )
}
