import { Router, Response } from 'express';
import { prisma } from '../utils/database';
import { authenticateToken } from '../middleware/auth';
import { 
  validateCreateTrade, 
  validatePagination, 
  validateTradeFilters 
} from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { BlockchainService } from '../services/blockchain';
import { 
  AuthenticatedRequest, 
  ApiResponse, 
  PaginatedResponse, 
  CreateTradeRequest,
  TradeFilters,
  NotFoundError,
  ValidationError 
} from '../types';
import { logger } from '../utils/logger';
import crypto from 'crypto';

const router = Router();

/**
 * GET /api/trades
 * Get user's trades with filtering and pagination
 */
router.get('/', 
  authenticateToken,
  validatePagination,
  validateTradeFilters,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const userAddress = req.user!.address;

    // Build filters - user must be either maker or taker
    const baseFilter = {
      OR: [
        { makerAddress: userAddress },
        { takerAddress: userAddress },
      ],
    };

    const queryFilters = req.query as TradeFilters;
    const additionalFilters: any = {};

    if (queryFilters.status) {
      additionalFilters.status = queryFilters.status;
    }
    
    if (queryFilters.direction) {
      additionalFilters.direction = queryFilters.direction;
    }

    const filters = Object.keys(additionalFilters).length > 0 
      ? { AND: [baseFilter, additionalFilters] }
      : baseFilter;

    try {
      const total = await prisma.trade.count({ where: filters });
      
      const trades = await prisma.trade.findMany({
        where: filters,
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
        include: {
          offer: {
            select: {
              id: true,
              direction: true,
              assetTokenAddress: true,
              priceCurrency: true,
              metadata: true,
            },
          },
        },
      });

      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: trades,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      } as PaginatedResponse<typeof trades[0]>);
    } catch (error) {
      logger.error('Failed to fetch trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch trades',
      } as ApiResponse);
    }
  })
);

/**
 * GET /api/trades/:tradeId
 * Get specific trade details
 */
router.get('/:tradeId', 
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const tradeId = parseInt(req.params.tradeId);
    const userAddress = req.user!.address;
    
    if (isNaN(tradeId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid trade ID',
      } as ApiResponse);
    }

    try {
      const trade = await prisma.trade.findUnique({
        where: { tradeId: BigInt(tradeId) },
        include: {
          offer: true,
        },
      });

      if (!trade) {
        throw new NotFoundError('Trade not found');
      }

      // Check if user is participant
      if (trade.makerAddress !== userAddress && trade.takerAddress !== userAddress) {
        return res.status(403).json({
          success: false,
          error: 'Access denied',
        } as ApiResponse);
      }

      // Get latest on-chain data
      const blockchainService = BlockchainService.getInstance();
      try {
        const onChainTrade = await blockchainService.getTradeById(BigInt(tradeId));
        // You could merge on-chain data with database data here if needed
      } catch (error) {
        logger.warn(`Failed to fetch on-chain data for trade ${tradeId}:`, error);
      }

      res.json({
        success: true,
        data: trade,
      } as ApiResponse);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
        } as ApiResponse);
      } else {
        logger.error('Failed to fetch trade:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch trade',
        } as ApiResponse);
      }
    }
  })
);

/**
 * POST /api/trades
 * Create a new trade from an offer
 */
router.post('/', 
  authenticateToken,
  validateCreateTrade,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const tradeData: CreateTradeRequest = req.body;
    const takerAddress = req.user!.address;

    try {
      // Get the offer
      const offer = await prisma.tradeOffer.findUnique({
        where: { id: tradeData.offerId },
      });

      if (!offer) {
        throw new NotFoundError('Offer not found');
      }

      if (offer.status !== 'ACTIVE') {
        throw new ValidationError('Offer is not active');
      }

      if (offer.makerAddress === takerAddress) {
        throw new ValidationError('Cannot trade with yourself');
      }

      // Validate quantity
      const quantity = parseFloat(tradeData.quantity);
      const minSize = parseFloat(offer.minTradeSize.toString());
      const maxSize = parseFloat(offer.maxTradeSize.toString());

      if (quantity < minSize || quantity > maxSize) {
        throw new ValidationError(`Quantity must be between ${minSize} and ${maxSize}`);
      }

      // Calculate price for this quantity
      const totalPrice = quantity * parseFloat(offer.price.toString());

      // Prepare contract parameters
      const contractParams = {
        taker: takerAddress as `0x${string}`,
        depositToken: offer.depositTokenAddress as `0x${string}`,
        price: BigInt(Math.floor(totalPrice * 1e18)), // Convert to wei
        deposit: BigInt(offer.depositAmount.toString()),
        fundingWindow: Number(offer.fundingWindowSeconds),
        direction: offer.direction === 'SELL' ? 0 : 1, // MakerSells = 0, MakerBuys = 1
        agreementHash: tradeData.agreementHash as `0x${string}`,
      };

      // Return the parameters for frontend to execute
      res.json({
        success: true,
        data: {
          contractParams,
          offer: {
            id: offer.id,
            makerAddress: offer.makerAddress,
            direction: offer.direction,
            assetTokenAddress: offer.assetTokenAddress,
            quantity: tradeData.quantity,
            totalPrice: totalPrice.toString(),
            depositAmount: offer.depositAmount.toString(),
            fundingWindowSeconds: offer.fundingWindowSeconds.toString(),
          },
        },
        message: 'Trade parameters prepared. Please confirm the transaction in your wallet.',
      } as ApiResponse);
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        const statusCode = error instanceof NotFoundError ? 404 : 400;
        res.status(statusCode).json({
          success: false,
          error: error.message,
        } as ApiResponse);
      } else {
        logger.error('Failed to prepare trade:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to prepare trade',
        } as ApiResponse);
      }
    }
  })
);

/**
 * POST /api/trades/sync-event
 * Internal endpoint for syncing blockchain events
 * This could be called by a webhook or internal service
 */
router.post('/sync-event', 
  // Add authentication for internal services here
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // This endpoint could be used for manual event syncing
    // or webhook integration with blockchain monitoring services
    
    res.json({
      success: true,
      message: 'Event sync endpoint - implementation depends on your architecture',
    } as ApiResponse);
  })
);

/**
 * GET /api/trades/stats
 * Get trading statistics for the user
 */
router.get('/stats/user', 
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userAddress = req.user!.address;

    try {
      const stats = await prisma.trade.groupBy({
        by: ['status'],
        where: {
          OR: [
            { makerAddress: userAddress },
            { takerAddress: userAddress },
          ],
        },
        _count: {
          status: true,
        },
      });

      const totalTrades = await prisma.trade.count({
        where: {
          OR: [
            { makerAddress: userAddress },
            { takerAddress: userAddress },
          ],
        },
      });

      const asMaker = await prisma.trade.count({
        where: { makerAddress: userAddress },
      });

      const asTaker = await prisma.trade.count({
        where: { takerAddress: userAddress },
      });

      const statusCounts = stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      }, {} as Record<string, number>);

      res.json({
        success: true,
        data: {
          totalTrades,
          asMaker,
          asTaker,
          byStatus: statusCounts,
        },
      } as ApiResponse);
    } catch (error) {
      logger.error('Failed to fetch trade stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch statistics',
      } as ApiResponse);
    }
  })
);

export { router as tradesRoutes };
