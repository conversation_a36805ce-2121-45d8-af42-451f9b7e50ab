import { Router, Response } from 'express';
import { prisma } from '../utils/database';
import { authenticateToken, optionalAuth } from '../middleware/auth';
import { 
  validateCreateOffer, 
  validatePagination, 
  validateOfferFilters 
} from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { 
  AuthenticatedRequest, 
  ApiResponse, 
  PaginatedResponse, 
  CreateOfferRequest,
  OfferFilters,
  NotFoundError,
  AuthorizationError 
} from '../types';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/offers
 * Get all active trade offers with optional filtering and pagination
 */
router.get('/', 
  validatePagination,
  validateOfferFilters,
  optionalAuth,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;

    // Build filters
    const filters: any = {
      status: 'ACTIVE', // Only show active offers by default
    };

    const queryFilters = req.query as OfferFilters;
    
    if (queryFilters.direction) {
      filters.direction = queryFilters.direction;
    }
    
    if (queryFilters.assetTokenAddress) {
      filters.assetTokenAddress = queryFilters.assetTokenAddress.toLowerCase();
    }
    
    if (queryFilters.priceCurrency) {
      filters.priceCurrency = queryFilters.priceCurrency;
    }
    
    if (queryFilters.makerAddress) {
      filters.makerAddress = queryFilters.makerAddress.toLowerCase();
    }

    // Price range filters
    const priceFilter: any = {};
    if (queryFilters.minPrice) {
      priceFilter.gte = queryFilters.minPrice;
    }
    if (queryFilters.maxPrice) {
      priceFilter.lte = queryFilters.maxPrice;
    }
    if (Object.keys(priceFilter).length > 0) {
      filters.price = priceFilter;
    }

    try {
      // Get total count
      const total = await prisma.tradeOffer.count({ where: filters });
      
      // Get offers
      const offers = await prisma.tradeOffer.findMany({
        where: filters,
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
        select: {
          id: true,
          makerAddress: true,
          direction: true,
          assetTokenAddress: true,
          assetTotalQuantity: true,
          price: true,
          priceCurrency: true,
          minTradeSize: true,
          maxTradeSize: true,
          depositTokenAddress: true,
          depositAmount: true,
          fundingWindowSeconds: true,
          status: true,
          createdAt: true,
          metadata: true,
          _count: {
            select: { trades: true }
          }
        },
      });

      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: offers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      } as PaginatedResponse<typeof offers[0]>);
    } catch (error) {
      logger.error('Failed to fetch offers:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch offers',
      } as ApiResponse);
    }
  })
);

/**
 * GET /api/offers/:id
 * Get specific offer details
 */
router.get('/:id', 
  optionalAuth,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const offerId = parseInt(req.params.id);
    
    if (isNaN(offerId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid offer ID',
      } as ApiResponse);
    }

    try {
      const offer = await prisma.tradeOffer.findUnique({
        where: { id: offerId },
        include: {
          trades: {
            select: {
              id: true,
              tradeId: true,
              status: true,
              createdAt: true,
            },
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!offer) {
        throw new NotFoundError('Offer not found');
      }

      res.json({
        success: true,
        data: offer,
      } as ApiResponse);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
        } as ApiResponse);
      } else {
        logger.error('Failed to fetch offer:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch offer',
        } as ApiResponse);
      }
    }
  })
);

/**
 * POST /api/offers
 * Create a new trade offer
 */
router.post('/', 
  authenticateToken,
  validateCreateOffer,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const offerData: CreateOfferRequest = req.body;
    const makerAddress = req.user!.address;

    try {
      const offer = await prisma.tradeOffer.create({
        data: {
          makerAddress,
          direction: offerData.direction,
          assetTokenAddress: offerData.assetTokenAddress.toLowerCase(),
          assetTotalQuantity: offerData.assetTotalQuantity,
          price: offerData.price,
          priceCurrency: offerData.priceCurrency,
          minTradeSize: offerData.minTradeSize,
          maxTradeSize: offerData.maxTradeSize,
          depositTokenAddress: offerData.depositTokenAddress.toLowerCase(),
          depositAmount: offerData.depositAmount,
          fundingWindowSeconds: BigInt(offerData.fundingWindowSeconds),
          metadata: offerData.metadata || {},
        },
      });

      logger.info(`Offer created: ${offer.id} by ${makerAddress}`);

      res.status(201).json({
        success: true,
        data: offer,
        message: 'Offer created successfully',
      } as ApiResponse);
    } catch (error) {
      logger.error('Failed to create offer:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create offer',
      } as ApiResponse);
    }
  })
);

/**
 * PUT /api/offers/:id/deactivate
 * Deactivate an offer (only by maker)
 */
router.put('/:id/deactivate', 
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const offerId = parseInt(req.params.id);
    const userAddress = req.user!.address;
    
    if (isNaN(offerId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid offer ID',
      } as ApiResponse);
    }

    try {
      // Check if offer exists and user is the maker
      const offer = await prisma.tradeOffer.findUnique({
        where: { id: offerId },
      });

      if (!offer) {
        throw new NotFoundError('Offer not found');
      }

      if (offer.makerAddress !== userAddress) {
        throw new AuthorizationError('Only the maker can deactivate this offer');
      }

      if (offer.status !== 'ACTIVE') {
        return res.status(400).json({
          success: false,
          error: 'Offer is not active',
        } as ApiResponse);
      }

      // Update offer status
      const updatedOffer = await prisma.tradeOffer.update({
        where: { id: offerId },
        data: { status: 'INACTIVE' },
      });

      logger.info(`Offer deactivated: ${offerId} by ${userAddress}`);

      res.json({
        success: true,
        data: updatedOffer,
        message: 'Offer deactivated successfully',
      } as ApiResponse);
    } catch (error) {
      if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
        } as ApiResponse);
      } else if (error instanceof AuthorizationError) {
        res.status(403).json({
          success: false,
          error: error.message,
        } as ApiResponse);
      } else {
        logger.error('Failed to deactivate offer:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to deactivate offer',
        } as ApiResponse);
      }
    }
  })
);

/**
 * GET /api/offers/my
 * Get current user's offers
 */
router.get('/my/offers', 
  authenticateToken,
  validatePagination,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;
    const userAddress = req.user!.address;

    try {
      const total = await prisma.tradeOffer.count({
        where: { makerAddress: userAddress },
      });
      
      const offers = await prisma.tradeOffer.findMany({
        where: { makerAddress: userAddress },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
        include: {
          _count: {
            select: { trades: true }
          }
        },
      });

      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: offers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      } as PaginatedResponse<typeof offers[0]>);
    } catch (error) {
      logger.error('Failed to fetch user offers:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch your offers',
      } as ApiResponse);
    }
  })
);

export { router as offersRoutes };
