import { useState, useCallback, useEffect } from 'react'
import { useAccount, useSignMessage } from 'wagmi'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'

import { authApi, setAuthToken } from '@/lib/api'
import type { User, AuthChallenge } from '@/types'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export function useAuth() {
  const { address, isConnected } = useAccount()
  const { signMessageAsync } = useSignMessage()
  const queryClient = useQueryClient()
  
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  })

  // Query to get current user info (only if we have a token)
  const { data: userInfo, isLoading: isLoadingUser } = useQuery({
    queryKey: ['auth', 'me'],
    queryFn: async () => {
      const response = await authApi.getMe()
      return response.data.data
    },
    enabled: !!localStorage.getItem('auth_token'),
    retry: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Update auth state when user info changes
  useEffect(() => {
    if (userInfo) {
      setAuthState({
        user: userInfo,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      })
    } else {
      setAuthState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false,
        isLoading: isLoadingUser,
      }))
    }
  }, [userInfo, isLoadingUser])

  // Get challenge mutation
  const getChallengeMutation = useMutation({
    mutationFn: async (address: string) => {
      const response = await authApi.getChallenge(address)
      return response.data.data
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to get challenge')
    },
  })

  // Verify signature mutation
  const verifySignatureMutation = useMutation({
    mutationFn: async (data: { address: string; signature: string; nonce: string }) => {
      const response = await authApi.verifySignature(data)
      return response.data.data
    },
    onSuccess: (data) => {
      setAuthToken(data.token)
      queryClient.invalidateQueries({ queryKey: ['auth', 'me'] })
      toast.success('Successfully authenticated!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Authentication failed')
    },
  })

  // Login function
  const login = useCallback(async () => {
    if (!address || !isConnected) {
      toast.error('Please connect your wallet first')
      return false
    }

    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))

      // Step 1: Get challenge
      const challenge: AuthChallenge = await getChallengeMutation.mutateAsync(address)

      // Step 2: Sign the challenge message
      const signature = await signMessageAsync({
        message: challenge.message,
      })

      // Step 3: Verify signature and get token
      await verifySignatureMutation.mutateAsync({
        address,
        signature,
        nonce: challenge.nonce,
      })

      return true
    } catch (error: any) {
      console.error('Login error:', error)
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Login failed',
      }))
      
      // Handle user rejection
      if (error.message?.includes('User rejected')) {
        toast.error('Signature rejected')
      }
      
      return false
    }
  }, [address, isConnected, signMessageAsync, getChallengeMutation, verifySignatureMutation])

  // Logout function
  const logout = useCallback(() => {
    setAuthToken(null)
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    })
    queryClient.clear()
    toast.success('Logged out successfully')
  }, [queryClient])

  // Auto-logout when wallet disconnects
  useEffect(() => {
    if (!isConnected && authState.isAuthenticated) {
      logout()
    }
  }, [isConnected, authState.isAuthenticated, logout])

  // Check if current user matches connected wallet
  const isWalletMismatch = authState.user && address && 
    authState.user.address.toLowerCase() !== address.toLowerCase()

  return {
    ...authState,
    login,
    logout,
    isWalletMismatch,
    isLoading: authState.isLoading || getChallengeMutation.isPending || verifySignatureMutation.isPending,
  }
}
