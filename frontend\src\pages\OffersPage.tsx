import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Link } from 'react-router-dom'
import { 
  SearchIcon, 
  FilterIcon, 
  PlusIcon,
  ArrowUpDownIcon,
  ClockIcon,
  UserIcon
} from 'lucide-react'

import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { TradeOffer, TradeDirection } from '@/types'
import { formatToken, formatDate, truncateAddress } from '@/lib/utils'
import { cn } from '@/lib/utils'

const DIRECTION_LABELS = {
  [TradeDirection.MakerSells]: 'Selling',
  [TradeDirection.MakerBuys]: 'Buying'
}

const DIRECTION_COLORS = {
  [TradeDirection.MakerSells]: 'text-red-600 bg-red-50',
  [TradeDirection.MakerBuys]: 'text-green-600 bg-green-50'
}

export default function OffersPage() {
  const { isAuthenticated } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDirection, setSelectedDirection] = useState<TradeDirection | 'all'>('all')
  const [sortBy, setSortBy] = useState<'created_at' | 'price' | 'deposit_amount'>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  const { data: offers, isLoading, error } = useQuery({
    queryKey: ['offers', { 
      search: searchTerm, 
      direction: selectedDirection === 'all' ? undefined : selectedDirection,
      sortBy,
      sortOrder
    }],
    queryFn: () => api.offers.getOffers({
      search: searchTerm || undefined,
      direction: selectedDirection === 'all' ? undefined : selectedDirection,
      sortBy,
      sortOrder,
      limit: 50
    })
  })

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load offers</div>
        <button 
          onClick={() => window.location.reload()} 
          className="btn-primary"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trading Offers</h1>
          <p className="text-gray-600">Browse available OTC trading opportunities</p>
        </div>
        {isAuthenticated && (
          <Link to="/create-offer" className="btn-primary">
            <PlusIcon className="w-4 h-4 mr-2" />
            Create Offer
          </Link>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by token, maker address..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10 w-full"
            />
          </div>

          {/* Direction Filter */}
          <select
            value={selectedDirection}
            onChange={(e) => setSelectedDirection(e.target.value as TradeDirection | 'all')}
            className="input w-full sm:w-auto"
          >
            <option value="all">All Directions</option>
            <option value={TradeDirection.MakerSells}>Selling</option>
            <option value={TradeDirection.MakerBuys}>Buying</option>
          </select>
        </div>

        {/* Sort Options */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-500 flex items-center">
            <FilterIcon className="w-4 h-4 mr-1" />
            Sort by:
          </span>
          {[
            { key: 'created_at', label: 'Date' },
            { key: 'price', label: 'Price' },
            { key: 'deposit_amount', label: 'Amount' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => handleSort(key as typeof sortBy)}
              className={cn(
                'btn-sm flex items-center gap-1',
                sortBy === key ? 'btn-primary' : 'btn-secondary'
              )}
            >
              {label}
              {sortBy === key && (
                <ArrowUpDownIcon className="w-3 h-3" />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Offers List */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow-sm border animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
            </div>
          ))}
        </div>
      ) : offers?.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm border">
          <div className="text-gray-500 mb-4">No offers found</div>
          {isAuthenticated && (
            <Link to="/create-offer" className="btn-primary">
              Create the first offer
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {offers?.map((offer) => (
            <OfferCard key={offer.id} offer={offer} />
          ))}
        </div>
      )}
    </div>
  )
}

interface OfferCardProps {
  offer: TradeOffer
}

function OfferCard({ offer }: OfferCardProps) {
  const { isAuthenticated } = useAuth()

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-2">
          <span className={cn(
            'px-2 py-1 rounded-full text-xs font-medium',
            DIRECTION_COLORS[offer.direction]
          )}>
            {DIRECTION_LABELS[offer.direction]}
          </span>
          <span className="text-sm text-gray-500">
            {offer.deposit_token_symbol}
          </span>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Price</div>
          <div className="font-semibold">
            ${formatToken(offer.price, 2)}
          </div>
        </div>
      </div>

      {/* Amount */}
      <div className="mb-4">
        <div className="text-sm text-gray-500">Deposit Amount</div>
        <div className="text-lg font-semibold">
          {formatToken(offer.deposit_amount)} {offer.deposit_token_symbol}
        </div>
      </div>

      {/* Maker Info */}
      <div className="flex items-center gap-2 mb-4">
        <UserIcon className="w-4 h-4 text-gray-400" />
        <span className="text-sm text-gray-600">
          {truncateAddress(offer.maker_address)}
        </span>
      </div>

      {/* Funding Window */}
      <div className="flex items-center gap-2 mb-4">
        <ClockIcon className="w-4 h-4 text-gray-400" />
        <span className="text-sm text-gray-600">
          {Math.floor(offer.funding_window / 3600)}h funding window
        </span>
      </div>

      {/* Description */}
      {offer.description && (
        <div className="mb-4">
          <div className="text-sm text-gray-600 line-clamp-2">
            {offer.description}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="text-xs text-gray-500">
          {formatDate(offer.created_at)}
        </div>
        {isAuthenticated && offer.is_active && (
          <Link
            to={`/offers/${offer.id}`}
            className="btn-primary btn-sm"
          >
            View Details
          </Link>
        )}
      </div>
    </div>
  )
}
