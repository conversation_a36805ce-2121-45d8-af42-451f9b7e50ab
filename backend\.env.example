# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/otchub?schema=public"

# Redis Configuration (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="24h"

# Blockchain Configuration
RPC_URL="http://127.0.0.1:8545"
CHAIN_ID=31337
OTCHUB_CONTRACT_ADDRESS="0x5FbDB2315678afecb367f032d93F642f64180aa3"

# Contract Deployment Info (from contracts/deployments/)
MOCK_TOKEN_ADDRESS="0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512"

# Security
CORS_ORIGIN="http://localhost:5173"
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# Event Sync Configuration
SYNC_FROM_BLOCK=0
SYNC_BATCH_SIZE=1000
SYNC_INTERVAL_MS=5000
