import { useQuery } from '@tanstack/react-query'
import { Link } from 'react-router-dom'
import { 
  TrendingUpIcon,
  DollarSignIcon,
  ShoppingBagIcon,
  ArrowRightLeftIcon,
  PlusIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon
} from 'lucide-react'

import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { TradeStatus } from '@/types'
import { formatToken, formatDate } from '@/lib/utils'

export default function DashboardPage() {
  const { isAuthenticated, user } = useAuth()

  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['trade-stats'],
    queryFn: api.trades.getUserStats,
    enabled: isAuthenticated
  })

  const { data: recentTrades, isLoading: tradesLoading } = useQuery({
    queryKey: ['recent-trades'],
    queryFn: () => api.trades.getUserTrades({ limit: 5 }),
    enabled: isAuthenticated
  })

  const { data: myOffers, isLoading: offersLoading } = useQuery({
    queryKey: ['my-offers'],
    queryFn: () => api.offers.getMyOffers({ limit: 5 }),
    enabled: isAuthenticated
  })

  if (!isAuthenticated) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">
          Please connect and authenticate your wallet to view your dashboard
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">
          Welcome back! Here's your trading overview.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Trades"
          value={stats?.total_trades || 0}
          icon={ArrowRightLeftIcon}
          color="blue"
          loading={statsLoading}
        />
        <StatCard
          title="Completed Trades"
          value={stats?.completed_trades || 0}
          icon={CheckCircleIcon}
          color="green"
          loading={statsLoading}
        />
        <StatCard
          title="Total Volume"
          value={`$${formatToken(stats?.total_volume || 0, 2)}`}
          icon={DollarSignIcon}
          color="purple"
          loading={statsLoading}
        />
        <StatCard
          title="Success Rate"
          value={stats?.total_trades > 0 
            ? `${Math.round((stats.completed_trades / stats.total_trades) * 100)}%`
            : '0%'
          }
          icon={TrendingUpIcon}
          color="orange"
          loading={statsLoading}
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/create-offer"
            className="flex items-center gap-3 p-4 rounded-lg border-2 border-dashed border-gray-300 hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <PlusIcon className="w-6 h-6 text-primary-600" />
            <div>
              <div className="font-medium text-gray-900">Create New Offer</div>
              <div className="text-sm text-gray-500">Start a new trading offer</div>
            </div>
          </Link>
          
          <Link
            to="/offers"
            className="flex items-center gap-3 p-4 rounded-lg border-2 border-dashed border-gray-300 hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <EyeIcon className="w-6 h-6 text-primary-600" />
            <div>
              <div className="font-medium text-gray-900">Browse Offers</div>
              <div className="text-sm text-gray-500">Find trading opportunities</div>
            </div>
          </Link>
          
          <Link
            to="/trades"
            className="flex items-center gap-3 p-4 rounded-lg border-2 border-dashed border-gray-300 hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <ArrowRightLeftIcon className="w-6 h-6 text-primary-600" />
            <div>
              <div className="font-medium text-gray-900">View All Trades</div>
              <div className="text-sm text-gray-500">Check your trade history</div>
            </div>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Trades */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Trades</h2>
            <Link to="/trades" className="text-primary-600 hover:text-primary-700 text-sm">
              View all
            </Link>
          </div>
          
          {tradesLoading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : recentTrades?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ArrowRightLeftIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No trades yet</p>
              <Link to="/offers" className="text-primary-600 hover:text-primary-700 text-sm">
                Browse offers to get started
              </Link>
            </div>
          ) : (
            <div className="space-y-3">
              {recentTrades?.slice(0, 5).map((trade) => (
                <div key={trade.trade_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-sm">
                      Trade #{trade.trade_id}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatToken(trade.deposit_amount)} {trade.deposit_token_symbol}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(trade.status)}`}>
                      {getStatusLabel(trade.status)}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatDate(trade.created_at)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* My Active Offers */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">My Active Offers</h2>
            <Link to="/create-offer" className="text-primary-600 hover:text-primary-700 text-sm">
              Create new
            </Link>
          </div>
          
          {offersLoading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : myOffers?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ShoppingBagIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No active offers</p>
              <Link to="/create-offer" className="text-primary-600 hover:text-primary-700 text-sm">
                Create your first offer
              </Link>
            </div>
          ) : (
            <div className="space-y-3">
              {myOffers?.slice(0, 5).map((offer) => (
                <div key={offer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-sm">
                      {offer.direction === 0 ? 'Selling' : 'Buying'} {offer.deposit_token_symbol}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatToken(offer.deposit_amount)} @ ${formatToken(offer.price, 2)}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      offer.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {offer.is_active ? 'Active' : 'Inactive'}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatDate(offer.created_at)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Activity Timeline */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {/* This would be populated with recent activity items */}
          <div className="text-center py-8 text-gray-500">
            <ClockIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>Activity timeline coming soon</p>
          </div>
        </div>
      </div>
    </div>
  )
}

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  color: 'blue' | 'green' | 'purple' | 'orange'
  loading?: boolean
}

function StatCard({ title, value, icon: Icon, color, loading }: StatCardProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
    orange: 'text-orange-600 bg-orange-100'
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          {loading ? (
            <div className="h-8 bg-gray-200 rounded w-16 mt-1 animate-pulse"></div>
          ) : (
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          <Icon className="w-6 h-6" />
        </div>
      </div>
    </div>
  )
}

function getStatusColor(status: TradeStatus): string {
  switch (status) {
    case TradeStatus.Open:
      return 'bg-yellow-100 text-yellow-800'
    case TradeStatus.Funded:
      return 'bg-blue-100 text-blue-800'
    case TradeStatus.Settled:
      return 'bg-green-100 text-green-800'
    case TradeStatus.Cancelled:
      return 'bg-gray-100 text-gray-800'
    case TradeStatus.Disputed:
      return 'bg-red-100 text-red-800'
    case TradeStatus.AdminClosed:
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

function getStatusLabel(status: TradeStatus): string {
  switch (status) {
    case TradeStatus.Open:
      return 'Open'
    case TradeStatus.Funded:
      return 'Funded'
    case TradeStatus.Settled:
      return 'Settled'
    case TradeStatus.Cancelled:
      return 'Cancelled'
    case TradeStatus.Disputed:
      return 'Disputed'
    case TradeStatus.AdminClosed:
      return 'Admin Closed'
    default:
      return 'Unknown'
  }
}
