import { useState } from 'react'
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAccount } from 'wagmi'
import { 
  ArrowLeftIcon,
  UserIcon,
  ClockIcon,
  DollarSignIcon,
  ShieldCheckIcon,
  AlertTriangleIcon,
  ExternalLinkIcon
} from 'lucide-react'

import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { useCreateTrade } from '@/hooks/useCreateTrade'
import { TradeDirection } from '@/types'
import { formatToken, formatDate, truncateAddress } from '@/lib/utils'
import { cn } from '@/lib/utils'

const DIRECTION_LABELS = {
  [TradeDirection.MakerSells]: 'Selling',
  [TradeDirection.MakerBuys]: 'Buying'
}

const DIRECTION_COLORS = {
  [TradeDirection.MakerSells]: 'text-red-600 bg-red-50 border-red-200',
  [TradeDirection.MakerBuys]: 'text-green-600 bg-green-50 border-green-200'
}

export default function OfferDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { address } = useAccount()
  const { isAuthenticated } = useAuth()
  const queryClient = useQueryClient()
  
  const [showCreateTradeModal, setShowCreateTradeModal] = useState(false)
  const [agreementHash, setAgreementHash] = useState('')

  const { data: offer, isLoading, error } = useQuery({
    queryKey: ['offer', id],
    queryFn: () => api.offers.getOffer(id!),
    enabled: !!id
  })

  const { createTrade, isLoading: isCreatingTrade, error: createTradeError } = useCreateTrade()

  const deactivateOfferMutation = useMutation({
    mutationFn: () => api.offers.deactivateOffer(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['offer', id] })
      queryClient.invalidateQueries({ queryKey: ['offers'] })
    }
  })

  const handleCreateTrade = async () => {
    if (!offer || !address) return

    try {
      await createTrade({
        makerAddress: offer.maker_address,
        depositTokenAddress: offer.deposit_token_address,
        price: offer.price,
        depositAmount: offer.deposit_amount,
        fundingWindow: offer.funding_window,
        direction: offer.direction,
        agreementHash: agreementHash || '0x0000000000000000000000000000000000000000000000000000000000000000'
      })
      
      setShowCreateTradeModal(false)
      navigate('/trades')
    } catch (err) {
      console.error('Failed to create trade:', err)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !offer) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="text-red-600 mb-4">Failed to load offer</div>
        <button onClick={() => navigate('/offers')} className="btn-primary">
          Back to Offers
        </button>
      </div>
    )
  }

  const isMyOffer = address && offer.maker_address.toLowerCase() === address.toLowerCase()
  const canTakeOffer = isAuthenticated && !isMyOffer && offer.is_active

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/offers')}
          className="btn-secondary btn-sm"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back to Offers
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Offer Details</h1>
          <p className="text-gray-600">Review the trading terms</p>
        </div>
      </div>

      {/* Offer Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={cn(
              'flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium',
              DIRECTION_COLORS[offer.direction]
            )}>
              {DIRECTION_LABELS[offer.direction]} {offer.deposit_token_symbol}
            </div>
            <div className={cn(
              'px-2 py-1 rounded-full text-xs font-medium',
              offer.is_active 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            )}>
              {offer.is_active ? 'Active' : 'Inactive'}
            </div>
          </div>
          
          {isMyOffer && offer.is_active && (
            <button
              onClick={() => deactivateOfferMutation.mutate()}
              disabled={deactivateOfferMutation.isPending}
              className="btn-secondary btn-sm"
            >
              {deactivateOfferMutation.isPending ? 'Deactivating...' : 'Deactivate'}
            </button>
          )}
        </div>

        {/* Trading Terms */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex items-center gap-3">
            <DollarSignIcon className="w-5 h-5 text-gray-400" />
            <div>
              <div className="text-sm text-gray-500">Price</div>
              <div className="text-lg font-semibold">${formatToken(offer.price, 2)}</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <ShieldCheckIcon className="w-5 h-5 text-gray-400" />
            <div>
              <div className="text-sm text-gray-500">Deposit Amount</div>
              <div className="text-lg font-semibold">
                {formatToken(offer.deposit_amount)} {offer.deposit_token_symbol}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <ClockIcon className="w-5 h-5 text-gray-400" />
            <div>
              <div className="text-sm text-gray-500">Funding Window</div>
              <div className="text-lg font-semibold">
                {Math.floor(offer.funding_window / 3600)} hours
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Maker Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Maker Information</h2>
        <div className="flex items-center gap-3">
          <UserIcon className="w-5 h-5 text-gray-400" />
          <div>
            <div className="text-sm text-gray-500">Wallet Address</div>
            <div className="font-mono text-sm">{offer.maker_address}</div>
          </div>
          <a
            href={`https://etherscan.io/address/${offer.maker_address}`}
            target="_blank"
            rel="noopener noreferrer"
            className="btn-secondary btn-sm ml-auto"
          >
            <ExternalLinkIcon className="w-4 h-4 mr-1" />
            View on Etherscan
          </a>
        </div>
      </div>

      {/* Description */}
      {offer.description && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Description</h2>
          <p className="text-gray-700 whitespace-pre-wrap">{offer.description}</p>
        </div>
      )}

      {/* Token Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Token Information</h2>
        <div className="space-y-3">
          <div>
            <div className="text-sm text-gray-500">Contract Address</div>
            <div className="font-mono text-sm">{offer.deposit_token_address}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500">Symbol</div>
            <div className="font-semibold">{offer.deposit_token_symbol}</div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {canTakeOffer && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-start gap-4">
            <AlertTriangleIcon className="w-5 h-5 text-amber-500 mt-1" />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-2">Ready to Trade?</h3>
              <p className="text-gray-600 mb-4">
                By accepting this offer, you'll create a new trade that requires both parties to deposit collateral.
                Make sure you understand the terms and have the necessary funds available.
              </p>
              <button
                onClick={() => setShowCreateTradeModal(true)}
                className="btn-primary"
              >
                Accept Offer & Create Trade
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Trade Modal */}
      {showCreateTradeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Trade</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Agreement Hash (Optional)
                </label>
                <input
                  type="text"
                  value={agreementHash}
                  onChange={(e) => setAgreementHash(e.target.value)}
                  placeholder="0x..."
                  className="input w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Hash of any off-chain agreement or terms
                </p>
              </div>

              {createTradeError && (
                <div className="text-red-600 text-sm">{createTradeError}</div>
              )}
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowCreateTradeModal(false)}
                className="btn-secondary flex-1"
                disabled={isCreatingTrade}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateTrade}
                disabled={isCreatingTrade}
                className="btn-primary flex-1"
              >
                {isCreatingTrade ? 'Creating...' : 'Create Trade'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="font-medium">Created:</span> {formatDate(offer.created_at)}
          </div>
          <div>
            <span className="font-medium">Last Updated:</span> {formatDate(offer.updated_at)}
          </div>
        </div>
      </div>
    </div>
  )
}
