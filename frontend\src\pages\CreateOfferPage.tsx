import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { useAccount } from 'wagmi'
import { 
  ArrowLeftIcon,
  InfoIcon,
  DollarSignIcon,
  ClockIcon,
  FileTextIcon
} from 'lucide-react'

import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { TradeDirection, CreateOfferRequest } from '@/types'
import { cn } from '@/lib/utils'

const DIRECTION_OPTIONS = [
  {
    value: TradeDirection.MakerSells,
    label: 'I want to sell',
    description: 'You will receive payment and deliver the asset',
    color: 'border-red-200 bg-red-50 text-red-700'
  },
  {
    value: TradeDirection.MakerBuys,
    label: 'I want to buy',
    description: 'You will pay and receive the asset',
    color: 'border-green-200 bg-green-50 text-green-700'
  }
]

const COMMON_TOKENS = [
  { symbol: 'USDC', address: '******************************************' },
  { symbol: 'USDT', address: '******************************************' },
  { symbol: 'DAI', address: '******************************************' },
  { symbol: 'WETH', address: '******************************************' }
]

export default function CreateOfferPage() {
  const navigate = useNavigate()
  const { address } = useAccount()
  const { isAuthenticated } = useAuth()
  const queryClient = useQueryClient()

  const [formData, setFormData] = useState<CreateOfferRequest>({
    direction: TradeDirection.MakerSells,
    deposit_token_address: '',
    deposit_token_symbol: '',
    price: '',
    deposit_amount: '',
    funding_window: 24 * 3600, // 24 hours default
    description: '',
    agreement_hash: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const createOfferMutation = useMutation({
    mutationFn: api.offers.createOffer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['offers'] })
      navigate('/offers')
    },
    onError: (error: any) => {
      console.error('Failed to create offer:', error)
    }
  })

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.deposit_token_address) {
      newErrors.deposit_token_address = 'Token address is required'
    }
    if (!formData.deposit_token_symbol) {
      newErrors.deposit_token_symbol = 'Token symbol is required'
    }
    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Valid price is required'
    }
    if (!formData.deposit_amount || parseFloat(formData.deposit_amount) <= 0) {
      newErrors.deposit_amount = 'Valid deposit amount is required'
    }
    if (formData.funding_window < 3600) {
      newErrors.funding_window = 'Funding window must be at least 1 hour'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!isAuthenticated) {
      alert('Please connect and authenticate your wallet first')
      return
    }

    if (!validateForm()) {
      return
    }

    createOfferMutation.mutate(formData)
  }

  const handleTokenSelect = (token: typeof COMMON_TOKENS[0]) => {
    setFormData(prev => ({
      ...prev,
      deposit_token_address: token.address,
      deposit_token_symbol: token.symbol
    }))
  }

  if (!isAuthenticated) {
    return (
      <div className="max-w-md mx-auto text-center py-12">
        <div className="text-gray-600 mb-4">
          Please connect and authenticate your wallet to create offers
        </div>
        <button 
          onClick={() => navigate('/offers')}
          className="btn-secondary"
        >
          Browse Offers Instead
        </button>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/offers')}
          className="btn-secondary btn-sm"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Trading Offer</h1>
          <p className="text-gray-600">Set up your OTC trading terms</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Direction Selection */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Trading Direction
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {DIRECTION_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, direction: option.value }))}
                className={cn(
                  'p-4 rounded-lg border-2 text-left transition-colors',
                  formData.direction === option.value
                    ? option.color
                    : 'border-gray-200 hover:border-gray-300'
                )}
              >
                <div className="font-medium">{option.label}</div>
                <div className="text-sm opacity-75 mt-1">{option.description}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Token Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm border space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Token Information</h3>
          
          {/* Common Tokens */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Select
            </label>
            <div className="flex flex-wrap gap-2">
              {COMMON_TOKENS.map((token) => (
                <button
                  key={token.symbol}
                  type="button"
                  onClick={() => handleTokenSelect(token)}
                  className={cn(
                    'btn-sm',
                    formData.deposit_token_symbol === token.symbol
                      ? 'btn-primary'
                      : 'btn-secondary'
                  )}
                >
                  {token.symbol}
                </button>
              ))}
            </div>
          </div>

          {/* Token Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Token Contract Address
            </label>
            <input
              type="text"
              value={formData.deposit_token_address}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                deposit_token_address: e.target.value 
              }))}
              className={cn('input w-full', errors.deposit_token_address && 'border-red-500')}
              placeholder="0x..."
            />
            {errors.deposit_token_address && (
              <p className="text-red-500 text-sm mt-1">{errors.deposit_token_address}</p>
            )}
          </div>

          {/* Token Symbol */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Token Symbol
            </label>
            <input
              type="text"
              value={formData.deposit_token_symbol}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                deposit_token_symbol: e.target.value.toUpperCase() 
              }))}
              className={cn('input w-full', errors.deposit_token_symbol && 'border-red-500')}
              placeholder="USDC"
            />
            {errors.deposit_token_symbol && (
              <p className="text-red-500 text-sm mt-1">{errors.deposit_token_symbol}</p>
            )}
          </div>
        </div>

        {/* Trading Terms */}
        <div className="bg-white p-6 rounded-lg shadow-sm border space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Trading Terms</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSignIcon className="w-4 h-4 inline mr-1" />
                Price (USD)
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                className={cn('input w-full', errors.price && 'border-red-500')}
                placeholder="1.00"
              />
              {errors.price && (
                <p className="text-red-500 text-sm mt-1">{errors.price}</p>
              )}
            </div>

            {/* Deposit Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deposit Amount
              </label>
              <input
                type="number"
                step="0.000001"
                value={formData.deposit_amount}
                onChange={(e) => setFormData(prev => ({ ...prev, deposit_amount: e.target.value }))}
                className={cn('input w-full', errors.deposit_amount && 'border-red-500')}
                placeholder="1000"
              />
              {errors.deposit_amount && (
                <p className="text-red-500 text-sm mt-1">{errors.deposit_amount}</p>
              )}
            </div>
          </div>

          {/* Funding Window */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <ClockIcon className="w-4 h-4 inline mr-1" />
              Funding Window (hours)
            </label>
            <select
              value={formData.funding_window / 3600}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                funding_window: parseInt(e.target.value) * 3600 
              }))}
              className="input w-full"
            >
              <option value={1}>1 hour</option>
              <option value={6}>6 hours</option>
              <option value={12}>12 hours</option>
              <option value={24}>24 hours</option>
              <option value={48}>48 hours</option>
              <option value={72}>72 hours</option>
            </select>
          </div>
        </div>

        {/* Description */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FileTextIcon className="w-4 h-4 inline mr-1" />
            Description (Optional)
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={4}
            className="input w-full"
            placeholder="Additional details about your offer, payment methods, etc."
          />
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <InfoIcon className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Important Notes:</p>
              <ul className="space-y-1 text-blue-700">
                <li>• Both parties will need to deposit collateral before trading</li>
                <li>• The funding window determines how long counterparties have to deposit</li>
                <li>• Make sure to communicate payment methods clearly in the description</li>
                <li>• You can deactivate your offer at any time before it's matched</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate('/offers')}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={createOfferMutation.isPending}
            className="btn-primary"
          >
            {createOfferMutation.isPending ? (
              <div className="spinner-sm mr-2" />
            ) : null}
            Create Offer
          </button>
        </div>
      </form>
    </div>
  )
}
