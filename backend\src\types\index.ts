import { Request } from 'express';

// Extend Express Request type to include user info
export interface AuthenticatedRequest extends Request {
  user?: {
    address: string;
    iat?: number;
    exp?: number;
  };
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Trade Offer types
export interface CreateOfferRequest {
  direction: 'SELL' | 'BUY';
  assetTokenAddress: string;
  assetTotalQuantity: string;
  price: string;
  priceCurrency: string;
  minTradeSize: string;
  maxTradeSize: string;
  depositTokenAddress: string;
  depositAmount: string;
  fundingWindowSeconds: number;
  metadata?: Record<string, any>;
}

export interface OfferFilters {
  direction?: 'SELL' | 'BUY';
  assetTokenAddress?: string;
  priceCurrency?: string;
  minPrice?: string;
  maxPrice?: string;
  status?: string;
  makerAddress?: string;
}

// Trade types
export interface CreateTradeRequest {
  offerId: number;
  quantity: string;
  agreementHash: string;
}

export interface TradeFilters {
  status?: string;
  direction?: string;
  makerAddress?: string;
  takerAddress?: string;
}

// Auth types
export interface AuthChallenge {
  message: string;
  nonce: string;
  expiresAt: string;
}

export interface VerifySignatureRequest {
  address: string;
  signature: string;
  nonce: string;
}

export interface JwtPayload {
  address: string;
  iat: number;
  exp: number;
}

// Blockchain types
export interface ContractEvent {
  eventName: string;
  args: any;
  blockNumber: bigint;
  transactionHash: string;
  logIndex: number;
}

export interface TradeCreatedEvent {
  tradeId: bigint;
  maker: string;
  taker: string;
  depositToken: string;
  price: bigint;
  deposit: bigint;
  fundingDeadline: bigint;
  direction: number; // 0 = MakerSells, 1 = MakerBuys
  agreementHash: string;
}

export interface TradeFundedEvent {
  tradeId: bigint;
  funder: string;
  amount: bigint;
}

export interface TradeConfirmedEvent {
  tradeId: bigint;
  confirmer: string;
}

export interface TradeSettledEvent {
  tradeId: bigint;
  platformFee: bigint;
}

export interface TradeCancelledEvent {
  tradeId: bigint;
}

export interface TradeDisputedEvent {
  tradeId: bigint;
  disputer: string;
}

// Error types
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Validation error type
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

// Authentication error type
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

// Authorization error type
export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

// Not found error type
export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}
