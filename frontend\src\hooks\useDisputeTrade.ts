import { useState } from 'react'
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import { useQueryClient } from '@tanstack/react-query'

import { OTC_HUB_ABI, OTC_HUB_ADDRESS } from '@/lib/contracts'

interface DisputeTradeParams {
  tradeId: number
}

interface DisputeTradeResult {
  disputeTrade: (params: DisputeTradeParams) => Promise<void>
  isLoading: boolean
  error: string | null
  transactionHash?: string
}

export function useDisputeTrade(): DisputeTradeResult {
  const [error, setError] = useState<string | null>(null)
  const [transactionHash, setTransactionHash] = useState<string>()
  
  const queryClient = useQueryClient()

  const { writeContract, data: hash, isPending } = useWriteContract()
  
  const { isLoading: isConfirming } = useWaitForTransactionReceipt({
    hash,
    onSuccess: (receipt) => {
      console.log('Trade disputed successfully:', receipt)
      setTransactionHash(receipt.transactionHash)
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['trades'] })
      queryClient.invalidateQueries({ queryKey: ['trade-stats'] })
    },
    onError: (error) => {
      setError(`Dispute failed: ${error.message}`)
    }
  })

  const disputeTrade = async (params: DisputeTradeParams) => {
    try {
      setError(null)
      setTransactionHash(undefined)

      // Call the smart contract to dispute the trade
      writeContract({
        address: OTC_HUB_ADDRESS,
        abi: OTC_HUB_ABI,
        functionName: 'disputeTrade',
        args: [BigInt(params.tradeId)]
      })

    } catch (err: any) {
      setError(err.message || 'Failed to dispute trade')
    }
  }

  return {
    disputeTrade,
    isLoading: isPending || isConfirming,
    error,
    transactionHash
  }
}
