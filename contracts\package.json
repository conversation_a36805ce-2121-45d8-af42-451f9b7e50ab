{"name": "contracts", "version": "1.0.0", "main": "index.js", "scripts": {"test": "npx hardhat test", "test:gas": "REPORT_GAS=true npx hardhat test", "test:coverage": "npx hardhat coverage", "compile": "npx hardhat compile", "clean": "npx hardhat clean", "node": "npx hardhat node", "deploy:local": "npx hardhat run scripts/deploy.ts --network localhost", "deploy:testnet": "npx hardhat run scripts/deploy.ts --network sepolia", "deploy:advanced:local": "npx hardhat run scripts/deploy-advanced.ts --network localhost", "deploy:advanced:testnet": "npx hardhat run scripts/deploy-advanced.ts --network sepolia", "verify:sepolia": "npx hardhat verify --network sepolia", "size": "npx hardhat size-contracts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-network-helpers": "^1.0.13", "@nomicfoundation/hardhat-toolbox": "^6.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "ethers": "^6.14.4", "hardhat": "^2.25.0", "typechain": "^8.3.2"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0"}}