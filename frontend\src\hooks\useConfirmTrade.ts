import { useState } from 'react'
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import { useQueryClient } from '@tanstack/react-query'

import { OTC_HUB_ABI, OTC_HUB_ADDRESS } from '@/lib/contracts'

interface ConfirmTradeParams {
  tradeId: number
}

interface ConfirmTradeResult {
  confirmTrade: (params: ConfirmTradeParams) => Promise<void>
  isLoading: boolean
  error: string | null
  transactionHash?: string
}

export function useConfirmTrade(): ConfirmTradeResult {
  const [error, setError] = useState<string | null>(null)
  const [transactionHash, setTransactionHash] = useState<string>()
  
  const queryClient = useQueryClient()

  const { writeContract, data: hash, isPending } = useWriteContract()
  
  const { isLoading: isConfirming } = useWaitForTransactionReceipt({
    hash,
    onSuccess: (receipt) => {
      console.log('Trade confirmed successfully:', receipt)
      setTransactionHash(receipt.transactionHash)
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['trades'] })
      queryClient.invalidateQueries({ queryKey: ['trade-stats'] })
    },
    onError: (error) => {
      setError(`Confirmation failed: ${error.message}`)
    }
  })

  const confirmTrade = async (params: ConfirmTradeParams) => {
    try {
      setError(null)
      setTransactionHash(undefined)

      // Call the smart contract to confirm the trade
      writeContract({
        address: OTC_HUB_ADDRESS,
        abi: OTC_HUB_ABI,
        functionName: 'confirmTrade',
        args: [BigInt(params.tradeId)]
      })

    } catch (err: any) {
      setError(err.message || 'Failed to confirm trade')
    }
  }

  return {
    confirmTrade,
    isLoading: isPending || isConfirming,
    error,
    transactionHash
  }
}
