import { ReactNode } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAccount } from 'wagmi'
import { ConnectButton } from '@rainbow-me/rainbowkit'
import { 
  HomeIcon, 
  ShoppingBagIcon, 
  PlusIcon, 
  ArrowRightLeftIcon,
  LayoutDashboardIcon,
  MenuIcon,
  XIcon
} from 'lucide-react'
import { useState } from 'react'

import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'

interface LayoutProps {
  children: ReactNode
}

const navigation = [
  { name: 'Home', href: '/', icon: HomeIcon, public: true },
  { name: 'Offers', href: '/offers', icon: ShoppingBagIcon, public: true },
  { name: 'Create Offer', href: '/create-offer', icon: PlusIcon, public: false },
  { name: 'My Trades', href: '/trades', icon: ArrowRightLeftIcon, public: false },
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboardIcon, public: false },
]

export default function Layout({ children }: LayoutProps) {
  const location = useLocation()
  const { isConnected } = useAccount()
  const { isAuthenticated, login, logout, isLoading, isWalletMismatch } = useAuth()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const handleAuthAction = async () => {
    if (isAuthenticated) {
      logout()
    } else if (isConnected) {
      await login()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Logo and main navigation */}
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <Link to="/" className="text-xl font-bold text-primary-600">
                  OTC Hub
                </Link>
              </div>
              
              {/* Desktop navigation */}
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigation.map((item) => {
                  const isActive = location.pathname === item.href
                  const isVisible = item.public || isAuthenticated
                  
                  if (!isVisible) return null
                  
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={cn(
                        'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium',
                        isActive
                          ? 'border-primary-500 text-gray-900'
                          : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                      )}
                    >
                      <item.icon className="w-4 h-4 mr-2" />
                      {item.name}
                    </Link>
                  )
                })}
              </div>
            </div>

            {/* Right side - Auth and wallet */}
            <div className="flex items-center space-x-4">
              {/* Wallet mismatch warning */}
              {isWalletMismatch && (
                <div className="hidden sm:block text-sm text-warning-600 bg-warning-50 px-3 py-1 rounded-md">
                  Wallet mismatch
                </div>
              )}
              
              {/* Auth button */}
              {isConnected && (
                <button
                  onClick={handleAuthAction}
                  disabled={isLoading}
                  className={cn(
                    'btn btn-sm',
                    isAuthenticated ? 'btn-secondary' : 'btn-primary'
                  )}
                >
                  {isLoading ? (
                    <div className="spinner-sm" />
                  ) : isAuthenticated ? (
                    'Sign Out'
                  ) : (
                    'Sign In'
                  )}
                </button>
              )}
              
              {/* Connect wallet button */}
              <ConnectButton />
              
              {/* Mobile menu button */}
              <div className="sm:hidden">
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
                >
                  {mobileMenuOpen ? (
                    <XIcon className="block h-6 w-6" />
                  ) : (
                    <MenuIcon className="block h-6 w-6" />
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="sm:hidden">
            <div className="pt-2 pb-3 space-y-1">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href
                const isVisible = item.public || isAuthenticated
                
                if (!isVisible) return null
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => setMobileMenuOpen(false)}
                    className={cn(
                      'block pl-3 pr-4 py-2 border-l-4 text-base font-medium',
                      isActive
                        ? 'bg-primary-50 border-primary-500 text-primary-700'
                        : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'
                    )}
                  >
                    <div className="flex items-center">
                      <item.icon className="w-4 h-4 mr-3" />
                      {item.name}
                    </div>
                  </Link>
                )
              })}
            </div>
          </div>
        )}
      </nav>

      {/* Main content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-auto">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              © 2024 OTC Hub. Built with ❤️ for decentralized trading.
            </div>
            <div className="flex space-x-6 text-sm text-gray-500">
              <a href="#" className="hover:text-gray-900">
                Documentation
              </a>
              <a href="#" className="hover:text-gray-900">
                Support
              </a>
              <a href="#" className="hover:text-gray-900">
                GitHub
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
