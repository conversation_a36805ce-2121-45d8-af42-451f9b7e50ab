import { Link } from 'react-router-dom'
import { useAccount } from 'wagmi'
import { 
  ShieldCheckIcon, 
  ArrowRightLeftIcon, 
  UsersIcon,
  TrendingUpIcon,
  PlusIcon,
  EyeIcon
} from 'lucide-react'

import { useAuth } from '@/hooks/useAuth'

const features = [
  {
    name: 'Secure Escrow',
    description: 'Dual-deposit escrow system ensures both parties have skin in the game, reducing counterparty risk.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'Peer-to-Peer Trading',
    description: 'Direct trading between users without intermediaries, maintaining privacy and reducing costs.',
    icon: ArrowRightLeftIcon,
  },
  {
    name: 'Community Driven',
    description: 'Built by traders, for traders. Community governance and transparent operations.',
    icon: UsersIcon,
  },
  {
    name: 'Competitive Rates',
    description: 'Market-driven pricing ensures you get the best rates for your trades.',
    icon: TrendingUpIcon,
  },
]

const stats = [
  { name: 'Total Volume', value: '$2.4M', change: '+12%' },
  { name: 'Active Offers', value: '156', change: '+8%' },
  { name: 'Completed Trades', value: '1,247', change: '+23%' },
  { name: 'Active Users', value: '892', change: '+15%' },
]

export default function HomePage() {
  const { isConnected } = useAccount()
  const { isAuthenticated } = useAuth()

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <div className="text-center">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
          Decentralized OTC Trading
        </h1>
        <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
          Trade cryptocurrencies peer-to-peer with confidence. Our secure escrow system 
          and transparent processes ensure safe and fair trading for everyone.
        </p>
        <div className="mt-10 flex items-center justify-center gap-x-6">
          <Link
            to="/offers"
            className="btn-primary btn-lg"
          >
            <EyeIcon className="w-5 h-5 mr-2" />
            Browse Offers
          </Link>
          {isConnected && isAuthenticated && (
            <Link
              to="/create-offer"
              className="btn-secondary btn-lg"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Create Offer
            </Link>
          )}
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white py-12 sm:py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <dl className="grid grid-cols-1 gap-x-8 gap-y-16 text-center lg:grid-cols-4">
            {stats.map((stat) => (
              <div key={stat.name} className="mx-auto flex max-w-xs flex-col gap-y-4">
                <dt className="text-base leading-7 text-gray-600">{stat.name}</dt>
                <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900 sm:text-5xl">
                  {stat.value}
                </dd>
                <div className="text-sm text-success-600 font-medium">
                  {stat.change} from last month
                </div>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-white py-12 sm:py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary-600">
              Why Choose OTC Hub
            </h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need for safe OTC trading
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Our platform combines the best of decentralized finance with proven trading mechanisms 
              to create a secure and efficient OTC marketplace.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
              {features.map((feature) => (
                <div key={feature.name} className="relative pl-16">
                  <dt className="text-base font-semibold leading-7 text-gray-900">
                    <div className="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600">
                      <feature.icon className="h-6 w-6 text-white" aria-hidden="true" />
                    </div>
                    {feature.name}
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    {feature.description}
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* How it Works Section */}
      <div className="bg-gray-50 py-12 sm:py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary-600">
              Simple Process
            </h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              How OTC Trading Works
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <div className="grid grid-cols-1 gap-y-16 lg:grid-cols-3 lg:gap-x-8">
              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
                  <span className="text-2xl font-bold text-primary-600">1</span>
                </div>
                <h3 className="mt-6 text-lg font-semibold text-gray-900">
                  Create or Find Offer
                </h3>
                <p className="mt-2 text-base text-gray-600">
                  Browse existing offers or create your own with your preferred terms and rates.
                </p>
              </div>
              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
                  <span className="text-2xl font-bold text-primary-600">2</span>
                </div>
                <h3 className="mt-6 text-lg font-semibold text-gray-900">
                  Lock Deposits
                </h3>
                <p className="mt-2 text-base text-gray-600">
                  Both parties deposit collateral into the smart contract escrow for security.
                </p>
              </div>
              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
                  <span className="text-2xl font-bold text-primary-600">3</span>
                </div>
                <h3 className="mt-6 text-lg font-semibold text-gray-900">
                  Complete Trade
                </h3>
                <p className="mt-2 text-base text-gray-600">
                  Execute the trade off-chain, confirm completion, and receive your funds plus deposit back.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary-600">
        <div className="px-6 py-12 sm:px-6 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to start trading?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-200">
              Join thousands of traders who trust OTC Hub for their peer-to-peer cryptocurrency trades.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                to="/offers"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-primary-600 shadow-sm hover:bg-primary-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Get started
              </Link>
              <a href="#" className="text-sm font-semibold leading-6 text-white">
                Learn more <span aria-hidden="true">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
