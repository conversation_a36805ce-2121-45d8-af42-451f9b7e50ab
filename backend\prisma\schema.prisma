// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 链下广告表
model TradeOffer {
  id                    Int      @id @default(autoincrement())
  
  // 核心参数
  makerAddress          String   @map("maker_address") @db.VarChar(42)
  direction             String   @db.VarChar(10) // 'SELL' 或 'BUY'
  assetTokenAddress     String   @map("asset_token_address") @db.VarChar(42)
  assetTotalQuantity    Decimal  @map("asset_total_quantity") @db.Decimal(78, 18)
  price                 Decimal  @db.Decimal(78, 18)
  priceCurrency         String   @map("price_currency") @db.VarChar(10)
  minTradeSize          Decimal  @map("min_trade_size") @db.Decimal(78, 18)
  maxTradeSize          Decimal  @map("max_trade_size") @db.Decimal(78, 18)
  depositTokenAddress   String   @map("deposit_token_address") @db.VarChar(42)
  depositAmount         Decimal  @map("deposit_amount") @db.Decimal(78, 18)
  fundingWindowSeconds  BigInt   @map("funding_window_seconds")
  
  // 状态与元数据
  status                String   @default("ACTIVE") @db.VarChar(20)
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  metadata              Json     @default("{}")
  
  // 关联的链上交易
  trades                Trade[]
  
  @@map("trade_offers")
  @@index([makerAddress])
  @@index([status])
  @@index([assetTokenAddress])
}

// 链上交易表
model Trade {
  id                    Int      @id @default(autoincrement())
  tradeId               BigInt   @unique @map("trade_id") // 链上合约的tradeId
  
  // Trade 结构体核心参数
  makerAddress          String   @map("maker_address") @db.VarChar(42)
  takerAddress          String   @map("taker_address") @db.VarChar(42)
  depositTokenAddress   String   @map("deposit_token_address") @db.VarChar(42)
  price                 Decimal  @db.Decimal(78, 0) // uint256
  deposit               Decimal  @db.Decimal(78, 0) // uint256
  agreementHash         String   @map("agreement_hash") @db.VarChar(66)
  direction             String   @db.VarChar(20) // MakerSells/MakerBuys
  fundingDeadline       DateTime @map("funding_deadline")
  
  // 状态追踪
  status                String   @default("Open") @db.VarChar(50)
  makerFunded           Boolean  @default(false) @map("maker_funded")
  takerFunded           Boolean  @default(false) @map("taker_funded")
  makerConfirmed        Boolean  @default(false) @map("maker_confirmed")
  takerConfirmed        Boolean  @default(false) @map("taker_confirmed")
  disputerAddress       String?  @map("disputer_address") @db.VarChar(42)
  
  // 交易哈希与同步信息
  creationTxHash        String?  @unique @map("creation_tx_hash") @db.VarChar(66)
  lastEventTxHash       String?  @map("last_event_tx_hash") @db.VarChar(66)
  lastSyncedBlock       BigInt?  @map("last_synced_block")
  
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  // 关联链下广告
  offerId               Int?     @map("offer_id")
  offer                 TradeOffer? @relation(fields: [offerId], references: [id], onDelete: SetNull)
  
  @@map("trades")
  @@index([tradeId])
  @@index([makerAddress])
  @@index([takerAddress])
  @@index([status])
}

// 用户认证挑战表（用于签名验证）
model AuthChallenge {
  id          Int      @id @default(autoincrement())
  address     String   @db.VarChar(42)
  nonce       String   @unique
  message     String
  expiresAt   DateTime @map("expires_at")
  used        Boolean  @default(false)
  createdAt   DateTime @default(now()) @map("created_at")
  
  @@map("auth_challenges")
  @@index([address])
  @@index([nonce])
}

// 同步状态表（记录区块链同步进度）
model SyncStatus {
  id              Int      @id @default(autoincrement())
  contractAddress String   @unique @map("contract_address") @db.VarChar(42)
  lastSyncedBlock BigInt   @map("last_synced_block")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  @@map("sync_status")
}
