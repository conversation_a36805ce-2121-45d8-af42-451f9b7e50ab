import axios, { AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Token management
let authToken: string | null = null

export const setAuthToken = (token: string | null) => {
  authToken = token
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    localStorage.setItem('auth_token', token)
  } else {
    delete api.defaults.headers.common['Authorization']
    localStorage.removeItem('auth_token')
  }
}

// Initialize token from localStorage
const savedToken = localStorage.getItem('auth_token')
if (savedToken) {
  setAuthToken(savedToken)
}

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Token expired or invalid
      setAuthToken(null)
      toast.error('Session expired. Please reconnect your wallet.')
    } else if (error.response?.status === 403) {
      toast.error('Access denied')
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.')
    } else if (error.code === 'NETWORK_ERROR' || !error.response) {
      toast.error('Network error. Please check your connection.')
    }
    
    return Promise.reject(error)
  }
)

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Auth API
export const authApi = {
  getChallenge: (address: string) =>
    api.get<ApiResponse<{ message: string; nonce: string; expiresAt: string }>>(`/auth/challenge?address=${address}`),
  
  verifySignature: (data: { address: string; signature: string; nonce: string }) =>
    api.post<ApiResponse<{ token: string; address: string; expiresIn: string }>>('/auth/verify', data),
  
  getMe: () =>
    api.get<ApiResponse<{ address: string; issuedAt: string; expiresAt: string }>>('/auth/me'),
}

// Offers API
export interface CreateOfferData {
  direction: 'SELL' | 'BUY'
  assetTokenAddress: string
  assetTotalQuantity: string
  price: string
  priceCurrency: string
  minTradeSize: string
  maxTradeSize: string
  depositTokenAddress: string
  depositAmount: string
  fundingWindowSeconds: number
  metadata?: Record<string, any>
}

export interface OfferFilters {
  direction?: 'SELL' | 'BUY'
  assetTokenAddress?: string
  priceCurrency?: string
  minPrice?: string
  maxPrice?: string
  status?: string
  makerAddress?: string
  page?: number
  limit?: number
}

export const offersApi = {
  getOffers: (filters?: OfferFilters) =>
    api.get<PaginatedResponse<any>>('/offers', { params: filters }),
  
  getOffer: (id: number) =>
    api.get<ApiResponse<any>>(`/offers/${id}`),
  
  createOffer: (data: CreateOfferData) =>
    api.post<ApiResponse<any>>('/offers', data),
  
  deactivateOffer: (id: number) =>
    api.put<ApiResponse<any>>(`/offers/${id}/deactivate`),
  
  getMyOffers: (params?: { page?: number; limit?: number }) =>
    api.get<PaginatedResponse<any>>('/offers/my/offers', { params }),
}

// Trades API
export interface CreateTradeData {
  offerId: number
  quantity: string
  agreementHash: string
}

export interface TradeFilters {
  status?: string
  direction?: string
  makerAddress?: string
  takerAddress?: string
  page?: number
  limit?: number
}

export const tradesApi = {
  getTrades: (filters?: TradeFilters) =>
    api.get<PaginatedResponse<any>>('/trades', { params: filters }),
  
  getTrade: (tradeId: number) =>
    api.get<ApiResponse<any>>(`/trades/${tradeId}`),
  
  createTrade: (data: CreateTradeData) =>
    api.post<ApiResponse<any>>('/trades', data),
  
  getStats: () =>
    api.get<ApiResponse<any>>('/trades/stats/user'),
}

// Unified API client for frontend components
class ApiClient {
  async get<T>(url: string, config?: any): Promise<T> {
    const response = await api.get(url, config)
    return response.data.data || response.data
  }

  async post<T>(url: string, data?: any, config?: any): Promise<T> {
    const response = await api.post(url, data, config)
    return response.data.data || response.data
  }

  async put<T>(url: string, data?: any, config?: any): Promise<T> {
    const response = await api.put(url, data, config)
    return response.data.data || response.data
  }

  async patch<T>(url: string, data?: any, config?: any): Promise<T> {
    const response = await api.patch(url, data, config)
    return response.data.data || response.data
  }

  async delete<T>(url: string, config?: any): Promise<T> {
    const response = await api.delete(url, config)
    return response.data.data || response.data
  }
}

const apiClient = new ApiClient()

// Import types from types file
import type {
  TradeOffer,
  Trade,
  CreateOfferRequest,
  TradeDirection,
  TradeStatus
} from '@/types'

interface GetOffersParams {
  search?: string
  direction?: TradeDirection
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  limit?: number
  page?: number
}

interface GetTradesParams {
  status?: TradeStatus
  limit?: number
  page?: number
}

interface UserStats {
  total_trades: number
  completed_trades: number
  total_volume: number
}

interface PrepareTradeRequest {
  maker_address: string
  deposit_token_address: string
  price: string
  deposit_amount: string
  funding_window: number
  direction: TradeDirection
  agreement_hash: string
}

// Main API object used by components
export const api = {
  auth: {
    getChallenge: (address: string) =>
      apiClient.get<{ message: string; nonce: string; expiresAt: string }>(`/auth/challenge?address=${address}`),

    verify: (data: { address: string; signature: string; nonce: string }) =>
      apiClient.post<{ token: string; address: string; expiresIn: string }>('/auth/verify', data),

    me: () =>
      apiClient.get<{ address: string; issuedAt: string; expiresAt: string }>('/auth/me'),
  },

  offers: {
    getOffers: (params?: GetOffersParams) =>
      apiClient.get<TradeOffer[]>('/offers', { params }),

    getOffer: (id: string) =>
      apiClient.get<TradeOffer>(`/offers/${id}`),

    createOffer: (data: CreateOfferRequest) =>
      apiClient.post<TradeOffer>('/offers', data),

    getMyOffers: (params?: { limit?: number }) =>
      apiClient.get<TradeOffer[]>('/offers/my', { params }),

    deactivateOffer: (id: string) =>
      apiClient.patch<void>(`/offers/${id}/deactivate`),
  },

  trades: {
    getUserTrades: (params?: GetTradesParams) =>
      apiClient.get<Trade[]>('/trades', { params }),

    getTrade: (tradeId: string) =>
      apiClient.get<Trade>(`/trades/${tradeId}`),

    getUserStats: () =>
      apiClient.get<UserStats>('/trades/stats'),

    prepareTrade: (data: PrepareTradeRequest) =>
      apiClient.post<any>('/trades/prepare', data),
  },
}

export default api
