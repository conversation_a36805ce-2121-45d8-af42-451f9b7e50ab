import { Routes, Route } from 'react-router-dom'
import { useAccount } from 'wagmi'
import { useEffect } from 'react'

import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import OffersPage from './pages/OffersPage'
import OfferDetailPage from './pages/OfferDetailPage'
import CreateOfferPage from './pages/CreateOfferPage'
import TradesPage from './pages/TradesPage'
import TradeDetailPage from './pages/TradeDetailPage'
import DashboardPage from './pages/DashboardPage'
import { useAuth } from './hooks/useAuth'

import '@rainbow-me/rainbowkit/styles.css'

function App() {
  const { address, isConnected } = useAccount()
  const { logout } = useAuth()

  // Handle wallet disconnection
  useEffect(() => {
    if (!isConnected && address) {
      // Wallet was disconnected, clear auth
      logout()
    }
  }, [isConnected, address, logout])

  return (
    <Layout>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<HomePage />} />
        <Route path="/offers" element={<OffersPage />} />
        <Route path="/offers/:id" element={<OfferDetailPage />} />
        
        {/* Protected routes */}
        <Route path="/create-offer" element={<CreateOfferPage />} />
        <Route path="/trades" element={<TradesPage />} />
        <Route path="/trades/:tradeId" element={<TradeDetailPage />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        
        {/* Catch all route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Layout>
  )
}

// Simple 404 page component
function NotFoundPage() {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
        <p className="text-gray-600 mb-8">Page not found</p>
        <a 
          href="/" 
          className="btn-primary"
        >
          Go Home
        </a>
      </div>
    </div>
  )
}

export default App
