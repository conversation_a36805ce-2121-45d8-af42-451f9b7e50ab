import { Router, Request, Response } from 'express';
import { AuthService } from '../services/auth';
import { validateVerifySignature } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse, VerifySignatureRequest } from '../types';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/auth/challenge
 * Generate authentication challenge for wallet signature
 */
router.get('/challenge', asyncHandler(async (req: Request, res: Response) => {
  const { address } = req.query;

  if (!address || typeof address !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Address parameter is required',
    } as ApiResponse);
  }

  // Validate Ethereum address format
  if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid Ethereum address format',
    } as ApiResponse);
  }

  try {
    const challenge = await AuthService.generateChallenge(address);
    
    res.json({
      success: true,
      data: challenge,
    } as ApiResponse);
  } catch (error) {
    logger.error('Challenge generation failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate challenge',
    } as ApiResponse);
  }
}));

/**
 * POST /api/auth/verify
 * Verify wallet signature and issue JWT token
 */
router.post('/verify', 
  validateVerifySignature,
  asyncHandler(async (req: Request, res: Response) => {
    const { address, signature, nonce }: VerifySignatureRequest = req.body;

    try {
      const token = await AuthService.verifySignatureAndIssueToken(
        address,
        signature,
        nonce
      );

      res.json({
        success: true,
        data: {
          token,
          address: address.toLowerCase(),
          expiresIn: '24h',
        },
      } as ApiResponse);
    } catch (error: any) {
      logger.error('Signature verification failed:', error);
      
      const statusCode = error.statusCode || 401;
      res.status(statusCode).json({
        success: false,
        error: error.message || 'Authentication failed',
      } as ApiResponse);
    }
  })
);

/**
 * POST /api/auth/refresh
 * Refresh JWT token (optional endpoint for future use)
 */
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  // This endpoint could be implemented to refresh tokens
  // For now, we'll just return a not implemented response
  res.status(501).json({
    success: false,
    error: 'Token refresh not implemented',
  } as ApiResponse);
}));

/**
 * GET /api/auth/me
 * Get current user info from token
 */
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'No token provided',
    } as ApiResponse);
  }

  try {
    const decoded = AuthService.verifyToken(token);
    
    res.json({
      success: true,
      data: {
        address: decoded.address,
        issuedAt: new Date(decoded.iat * 1000).toISOString(),
        expiresAt: new Date(decoded.exp * 1000).toISOString(),
      },
    } as ApiResponse);
  } catch (error: any) {
    res.status(401).json({
      success: false,
      error: error.message || 'Invalid token',
    } as ApiResponse);
  }
}));

export { router as authRoutes };
