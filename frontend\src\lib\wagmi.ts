import { getDefaultConfig } from '@rainbow-me/rainbowkit'
import { mainnet, localhost, sepolia } from 'wagmi/chains'

// Get environment variables
const projectId = import.meta.env.VITE_WALLETCONNECT_PROJECT_ID || 'demo-project-id'
const chainId = parseInt(import.meta.env.VITE_CHAIN_ID || '31337')

// Define chains based on environment
const chains = [
  ...(chainId === 1 ? [mainnet] : []),
  ...(chainId === 11155111 ? [sepolia] : []),
  ...(chainId === 31337 ? [localhost] : []),
] as const

// Ensure we have at least one chain
const supportedChains = chains.length > 0 ? chains : [localhost]

export const config = getDefaultConfig({
  appName: import.meta.env.VITE_APP_NAME || 'OTC Hub',
  projectId,
  chains: supportedChains,
  ssr: false, // If your dApp uses server side rendering (SSR)
})

// Contract addresses
export const CONTRACTS = {
  OTCHUB: (import.meta.env.VITE_OTCHUB_CONTRACT_ADDRESS || '******************************************') as `0x${string}`,
  MOCK_TOKEN: (import.meta.env.VITE_MOCK_TOKEN_ADDRESS || '******************************************') as `0x${string}`,
}

// Chain configuration
export const CHAIN_CONFIG = {
  id: chainId,
  rpcUrl: import.meta.env.VITE_RPC_URL || 'http://127.0.0.1:8545',
}
