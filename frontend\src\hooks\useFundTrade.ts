import { useState } from 'react'
import { useWriteContract, useWaitForTransactionReceipt, useReadContract, useAccount } from 'wagmi'
import { parseUnits } from 'viem'
import { useQueryClient } from '@tanstack/react-query'

import { OTC_HUB_ABI, OTC_HUB_ADDRESS } from '@/lib/contracts'
import { ERC20_ABI } from '@/lib/contracts'

interface FundTradeParams {
  tradeId: number
  tokenAddress: string
  amount: string
  decimals?: number
}

interface FundTradeResult {
  fundTrade: (params: FundTradeParams) => Promise<void>
  isLoading: boolean
  error: string | null
  transactionHash?: string
  needsApproval: boolean
  approve: () => Promise<void>
  isApproving: boolean
}

export function useFundTrade(): FundTradeResult {
  const [error, setError] = useState<string | null>(null)
  const [transactionHash, setTransactionHash] = useState<string>()
  const [tokenAddress, setTokenAddress] = useState<string>()
  const [amount, setAmount] = useState<string>()

  const queryClient = useQueryClient()
  const { address: userAddress } = useAccount()

  // Check current allowance
  const { data: allowance } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'allowance',
    args: tokenAddress && userAddress ? [
      userAddress,
      OTC_HUB_ADDRESS
    ] : undefined,
    query: {
      enabled: !!(tokenAddress && userAddress)
    }
  })

  // Approval transaction
  const { 
    writeContract: writeApproval, 
    data: approvalHash, 
    isPending: isApprovePending 
  } = useWriteContract()
  
  const { isLoading: isApprovalConfirming } = useWaitForTransactionReceipt({
    hash: approvalHash,
    onSuccess: () => {
      console.log('Approval confirmed')
      // Refetch allowance
      queryClient.invalidateQueries({ queryKey: ['allowance'] })
    },
    onError: (error) => {
      setError(`Approval failed: ${error.message}`)
    }
  })

  // Fund trade transaction
  const { writeContract: writeFund, data: fundHash, isPending: isFundPending } = useWriteContract()
  
  const { isLoading: isFundConfirming } = useWaitForTransactionReceipt({
    hash: fundHash,
    onSuccess: (receipt) => {
      console.log('Trade funded successfully:', receipt)
      setTransactionHash(receipt.transactionHash)
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['trades'] })
      queryClient.invalidateQueries({ queryKey: ['trade-stats'] })
    },
    onError: (error) => {
      setError(`Funding failed: ${error.message}`)
    }
  })

  const needsApproval = tokenAddress && amount && allowance !== undefined
    ? allowance < parseUnits(amount, 18) // This should use proper token decimals
    : false

  const approve = async () => {
    if (!tokenAddress || !amount) {
      setError('Missing token address or amount')
      return
    }

    try {
      setError(null)
      
      const amountWei = parseUnits(amount, 18) // This should use proper token decimals
      
      writeApproval({
        address: tokenAddress as `0x${string}`,
        abi: ERC20_ABI,
        functionName: 'approve',
        args: [OTC_HUB_ADDRESS, amountWei]
      })
    } catch (err: any) {
      setError(err.message || 'Failed to approve token')
    }
  }

  const fundTrade = async (params: FundTradeParams) => {
    try {
      setError(null)
      setTransactionHash(undefined)
      setTokenAddress(params.tokenAddress)
      setAmount(params.amount)

      // Check if approval is needed first
      const amountWei = parseUnits(params.amount, params.decimals || 18)
      
      if (allowance !== undefined && allowance < amountWei) {
        setError('Token approval required. Please approve first.')
        return
      }

      // Call the smart contract to fund the trade
      writeFund({
        address: OTC_HUB_ADDRESS,
        abi: OTC_HUB_ABI,
        functionName: 'fundTrade',
        args: [BigInt(params.tradeId)]
      })

    } catch (err: any) {
      setError(err.message || 'Failed to fund trade')
    }
  }

  return {
    fundTrade,
    isLoading: isFundPending || isFundConfirming,
    error,
    transactionHash,
    needsApproval,
    approve,
    isApproving: isApprovePending || isApprovalConfirming
  }
}
