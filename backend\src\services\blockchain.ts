import { createPublicClient, createWalletClient, http, parseAbi, getContract, Address } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { mainnet, localhost } from 'viem/chains';
import { logger } from '../utils/logger';
import { prisma } from '../utils/database';
import { 
  TradeCreatedEvent, 
  TradeFundedEvent, 
  TradeConfirmedEvent, 
  TradeSettledEvent,
  TradeCancelledEvent,
  TradeDisputedEvent 
} from '../types';

// OtcHub contract ABI (simplified for events)
const OTCHUB_ABI = parseAbi([
  // Events
  'event TradeCreated(uint256 indexed tradeId, address indexed maker, address indexed taker, address depositToken, uint256 price, uint256 deposit, uint256 fundingDeadline, uint8 direction, bytes32 agreementHash)',
  'event TradeFunded(uint256 indexed tradeId, address indexed funder, uint256 amount)',
  'event TradeConfirmed(uint256 indexed tradeId, address indexed confirmer)',
  'event TradeSettled(uint256 indexed tradeId, uint256 platformFee)',
  'event TradeCancelled(uint256 indexed tradeId)',
  'event TradeDisputed(uint256 indexed tradeId, address indexed disputer)',
  'event DisputeCancelled(uint256 indexed tradeId, address indexed disputer)',
  
  // Read functions
  'function trades(uint256) view returns (address maker, address taker, address depositToken, uint256 price, uint256 deposit, uint256 fundingDeadline, uint8 direction, bytes32 agreementHash, uint8 status, bool makerFunded, bool takerFunded, bool makerConfirmed, bool takerConfirmed, address disputer)',
  'function nextTradeId() view returns (uint256)',
  'function platformFeeBps() view returns (uint256)',
  'function admin() view returns (address)',
  'function vault() view returns (address)',
  
  // Write functions
  'function createTrade(address taker, address depositToken, uint256 price, uint256 deposit, uint256 fundingWindow, uint8 direction, bytes32 agreementHash) returns (uint256)',
]);

export class BlockchainService {
  private static instance: BlockchainService;
  private publicClient: any;
  private walletClient: any;
  private contract: any;
  private isListening = false;

  private constructor() {}

  public static getInstance(): BlockchainService {
    if (!BlockchainService.instance) {
      BlockchainService.instance = new BlockchainService();
    }
    return BlockchainService.instance;
  }

  public async initialize(): Promise<void> {
    try {
      const rpcUrl = process.env.RPC_URL || 'http://127.0.0.1:8545';
      const chainId = parseInt(process.env.CHAIN_ID || '31337');
      const contractAddress = process.env.OTCHUB_CONTRACT_ADDRESS as Address;

      if (!contractAddress) {
        throw new Error('OTCHUB_CONTRACT_ADDRESS not configured');
      }

      // Determine chain
      const chain = chainId === 1 ? mainnet : localhost;

      // Create clients
      this.publicClient = createPublicClient({
        chain,
        transport: http(rpcUrl),
      });

      // Create contract instance
      this.contract = getContract({
        address: contractAddress,
        abi: OTCHUB_ABI,
        client: this.publicClient,
      });

      // Test connection
      const blockNumber = await this.publicClient.getBlockNumber();
      logger.info(`Connected to blockchain at block ${blockNumber}`);

      // Initialize sync status if not exists
      await this.initializeSyncStatus(contractAddress);

    } catch (error) {
      logger.error('Failed to initialize blockchain service:', error);
      throw error;
    }
  }

  private async initializeSyncStatus(contractAddress: string): Promise<void> {
    try {
      const existingStatus = await prisma.syncStatus.findUnique({
        where: { contractAddress },
      });

      if (!existingStatus) {
        const startBlock = BigInt(process.env.SYNC_FROM_BLOCK || '0');
        await prisma.syncStatus.create({
          data: {
            contractAddress,
            lastSyncedBlock: startBlock,
          },
        });
        logger.info(`Initialized sync status for contract ${contractAddress} from block ${startBlock}`);
      }
    } catch (error) {
      logger.error('Failed to initialize sync status:', error);
      throw error;
    }
  }

  public async startEventListener(): Promise<void> {
    if (this.isListening) {
      logger.warn('Event listener is already running');
      return;
    }

    try {
      this.isListening = true;
      logger.info('Starting blockchain event listener...');

      // Get last synced block
      const syncStatus = await prisma.syncStatus.findUnique({
        where: { contractAddress: process.env.OTCHUB_CONTRACT_ADDRESS! },
      });

      const fromBlock = syncStatus?.lastSyncedBlock || BigInt(0);

      // Listen to all events
      const unwatch = this.publicClient.watchContractEvent({
        address: this.contract.address,
        abi: OTCHUB_ABI,
        fromBlock,
        onLogs: (logs: any[]) => {
          this.handleContractEvents(logs);
        },
        onError: (error: Error) => {
          logger.error('Event listener error:', error);
          this.isListening = false;
          // Restart listener after delay
          setTimeout(() => {
            this.startEventListener();
          }, 5000);
        },
      });

      logger.info(`Event listener started from block ${fromBlock}`);
    } catch (error) {
      logger.error('Failed to start event listener:', error);
      this.isListening = false;
      throw error;
    }
  }

  private async handleContractEvents(logs: any[]): Promise<void> {
    for (const log of logs) {
      try {
        await this.processEvent(log);
        
        // Update sync status
        await prisma.syncStatus.update({
          where: { contractAddress: this.contract.address },
          data: { lastSyncedBlock: log.blockNumber },
        });
      } catch (error) {
        logger.error('Failed to process event:', error);
      }
    }
  }

  private async processEvent(log: any): Promise<void> {
    const { eventName, args, blockNumber, transactionHash } = log;

    logger.info(`Processing event: ${eventName} at block ${blockNumber}`);

    switch (eventName) {
      case 'TradeCreated':
        await this.handleTradeCreated(args as TradeCreatedEvent, transactionHash);
        break;
      case 'TradeFunded':
        await this.handleTradeFunded(args as TradeFundedEvent, transactionHash);
        break;
      case 'TradeConfirmed':
        await this.handleTradeConfirmed(args as TradeConfirmedEvent, transactionHash);
        break;
      case 'TradeSettled':
        await this.handleTradeSettled(args as TradeSettledEvent, transactionHash);
        break;
      case 'TradeCancelled':
        await this.handleTradeCancelled(args as TradeCancelledEvent, transactionHash);
        break;
      case 'TradeDisputed':
        await this.handleTradeDisputed(args as TradeDisputedEvent, transactionHash);
        break;
      default:
        logger.debug(`Unhandled event: ${eventName}`);
    }
  }

  private async handleTradeCreated(event: TradeCreatedEvent, txHash: string): Promise<void> {
    try {
      const direction = event.direction === 0 ? 'MakerSells' : 'MakerBuys';
      
      await prisma.trade.create({
        data: {
          tradeId: event.tradeId,
          makerAddress: event.maker.toLowerCase(),
          takerAddress: event.taker.toLowerCase(),
          depositTokenAddress: event.depositToken.toLowerCase(),
          price: event.price.toString(),
          deposit: event.deposit.toString(),
          agreementHash: event.agreementHash,
          direction,
          fundingDeadline: new Date(Number(event.fundingDeadline) * 1000),
          status: 'Open',
          creationTxHash: txHash,
          lastEventTxHash: txHash,
        },
      });

      logger.info(`Trade created: ${event.tradeId}`);
    } catch (error) {
      logger.error('Failed to handle TradeCreated event:', error);
    }
  }

  private async handleTradeFunded(event: TradeFundedEvent, txHash: string): Promise<void> {
    try {
      const trade = await prisma.trade.findUnique({
        where: { tradeId: event.tradeId },
      });

      if (!trade) {
        logger.error(`Trade not found: ${event.tradeId}`);
        return;
      }

      const isMaker = event.funder.toLowerCase() === trade.makerAddress;
      const updateData: any = {
        lastEventTxHash: txHash,
      };

      if (isMaker) {
        updateData.makerFunded = true;
      } else {
        updateData.takerFunded = true;
      }

      // Check if both parties have funded
      const updatedTrade = await prisma.trade.update({
        where: { tradeId: event.tradeId },
        data: updateData,
      });

      if (updatedTrade.makerFunded && updatedTrade.takerFunded) {
        await prisma.trade.update({
          where: { tradeId: event.tradeId },
          data: { status: 'Funded' },
        });
      }

      logger.info(`Trade funded by ${isMaker ? 'maker' : 'taker'}: ${event.tradeId}`);
    } catch (error) {
      logger.error('Failed to handle TradeFunded event:', error);
    }
  }

  private async handleTradeConfirmed(event: TradeConfirmedEvent, txHash: string): Promise<void> {
    try {
      const trade = await prisma.trade.findUnique({
        where: { tradeId: event.tradeId },
      });

      if (!trade) {
        logger.error(`Trade not found: ${event.tradeId}`);
        return;
      }

      const isMaker = event.confirmer.toLowerCase() === trade.makerAddress;
      const updateData: any = {
        lastEventTxHash: txHash,
      };

      if (isMaker) {
        updateData.makerConfirmed = true;
      } else {
        updateData.takerConfirmed = true;
      }

      await prisma.trade.update({
        where: { tradeId: event.tradeId },
        data: updateData,
      });

      logger.info(`Trade confirmed by ${isMaker ? 'maker' : 'taker'}: ${event.tradeId}`);
    } catch (error) {
      logger.error('Failed to handle TradeConfirmed event:', error);
    }
  }

  private async handleTradeSettled(event: TradeSettledEvent, txHash: string): Promise<void> {
    try {
      await prisma.trade.update({
        where: { tradeId: event.tradeId },
        data: {
          status: 'Settled',
          lastEventTxHash: txHash,
        },
      });

      logger.info(`Trade settled: ${event.tradeId}`);
    } catch (error) {
      logger.error('Failed to handle TradeSettled event:', error);
    }
  }

  private async handleTradeCancelled(event: TradeCancelledEvent, txHash: string): Promise<void> {
    try {
      await prisma.trade.update({
        where: { tradeId: event.tradeId },
        data: {
          status: 'Cancelled',
          lastEventTxHash: txHash,
        },
      });

      logger.info(`Trade cancelled: ${event.tradeId}`);
    } catch (error) {
      logger.error('Failed to handle TradeCancelled event:', error);
    }
  }

  private async handleTradeDisputed(event: TradeDisputedEvent, txHash: string): Promise<void> {
    try {
      await prisma.trade.update({
        where: { tradeId: event.tradeId },
        data: {
          status: 'Disputed',
          disputerAddress: event.disputer.toLowerCase(),
          lastEventTxHash: txHash,
        },
      });

      logger.info(`Trade disputed: ${event.tradeId}`);
    } catch (error) {
      logger.error('Failed to handle TradeDisputed event:', error);
    }
  }

  // Public methods for contract interaction
  public async getTradeById(tradeId: bigint): Promise<any> {
    try {
      return await this.contract.read.trades([tradeId]);
    } catch (error) {
      logger.error(`Failed to get trade ${tradeId}:`, error);
      throw error;
    }
  }

  public async getNextTradeId(): Promise<bigint> {
    try {
      return await this.contract.read.nextTradeId();
    } catch (error) {
      logger.error('Failed to get next trade ID:', error);
      throw error;
    }
  }
}
