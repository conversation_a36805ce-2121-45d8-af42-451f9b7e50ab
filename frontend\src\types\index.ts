// Trade Offer Types
export interface TradeOffer {
  id: number
  makerAddress: string
  direction: 'SELL' | 'BUY'
  assetTokenAddress: string
  assetTotalQuantity: string
  price: string
  priceCurrency: string
  minTradeSize: string
  maxTradeSize: string
  depositTokenAddress: string
  depositAmount: string
  fundingWindowSeconds: string
  status: 'ACTIVE' | 'INACTIVE' | 'FILLED'
  createdAt: string
  updatedAt: string
  metadata: Record<string, any>
  _count?: {
    trades: number
  }
  trades?: Trade[]
}

// Trade Types
export interface Trade {
  id: number
  tradeId: string
  makerAddress: string
  takerAddress: string
  depositTokenAddress: string
  price: string
  deposit: string
  agreementHash: string
  direction: 'MakerSells' | 'MakerBuys'
  fundingDeadline: string
  status: TradeStatus
  makerFunded: boolean
  takerFunded: boolean
  makerConfirmed: boolean
  takerConfirmed: boolean
  disputerAddress?: string
  creationTxHash?: string
  lastEventTxHash?: string
  lastSyncedBlock?: string
  createdAt: string
  updatedAt: string
  offerId?: number
  offer?: TradeOffer
}

export type TradeStatus = 
  | 'Open' 
  | 'Funded' 
  | 'Settled' 
  | 'Cancelled' 
  | 'Disputed' 
  | 'AdminClosed'

// Contract Types
export interface ContractParams {
  taker: `0x${string}`
  depositToken: `0x${string}`
  price: bigint
  deposit: bigint
  fundingWindow: number
  direction: number
  agreementHash: `0x${string}`
}

// User Types
export interface User {
  address: string
  issuedAt: string
  expiresAt: string
}

// Auth Types
export interface AuthChallenge {
  message: string
  nonce: string
  expiresAt: string
}

// Statistics Types
export interface TradeStats {
  totalTrades: number
  asMaker: number
  asTaker: number
  byStatus: Record<string, number>
}

// Form Types
export interface CreateOfferForm {
  direction: 'SELL' | 'BUY'
  assetTokenAddress: string
  assetTotalQuantity: string
  price: string
  priceCurrency: string
  minTradeSize: string
  maxTradeSize: string
  depositTokenAddress: string
  depositAmount: string
  fundingWindowHours: number
  paymentMethods?: string[]
  description?: string
}

export interface CreateTradeForm {
  quantity: string
  paymentMethod?: string
  notes?: string
}

// Filter Types
export interface OfferFilters {
  direction?: 'SELL' | 'BUY'
  assetTokenAddress?: string
  priceCurrency?: string
  minPrice?: string
  maxPrice?: string
  search?: string
}

export interface TradeFilters {
  status?: TradeStatus
  direction?: 'MakerSells' | 'MakerBuys'
  role?: 'maker' | 'taker' | 'all'
}

// UI Types
export interface LoadingState {
  isLoading: boolean
  error?: string
}

export interface PaginationState {
  page: number
  limit: number
  total: number
  totalPages: number
}

// Token Types
export interface Token {
  address: string
  symbol: string
  name: string
  decimals: number
  logoURI?: string
}

// Common token list
export const COMMON_TOKENS: Token[] = [
  {
    address: '******************************************',
    symbol: 'TEST',
    name: 'Test Token',
    decimals: 18,
  },
  // Add more tokens as needed
]

// Status color mapping
export const STATUS_COLORS: Record<TradeStatus | string, string> = {
  Open: 'blue',
  Funded: 'yellow',
  Settled: 'green',
  Cancelled: 'gray',
  Disputed: 'red',
  AdminClosed: 'purple',
  ACTIVE: 'green',
  INACTIVE: 'gray',
  FILLED: 'blue',
}

// Direction labels
export const DIRECTION_LABELS = {
  SELL: 'Selling',
  BUY: 'Buying',
  MakerSells: 'Maker Sells',
  MakerBuys: 'Maker Buys',
}

// Time constants
export const TIME_CONSTANTS = {
  HOUR: 60 * 60,
  DAY: 24 * 60 * 60,
  WEEK: 7 * 24 * 60 * 60,
}

// Default pagination
export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
}
