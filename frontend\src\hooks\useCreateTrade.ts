import { useState } from 'react'
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import { parseUnits } from 'viem'
import { useMutation, useQueryClient } from '@tanstack/react-query'

import { OTC_HUB_ABI, OTC_HUB_ADDRESS } from '@/lib/contracts'
import { api } from '@/lib/api'
import { TradeDirection } from '@/types'

interface CreateTradeParams {
  makerAddress: string
  depositTokenAddress: string
  price: string
  depositAmount: string
  fundingWindow: number
  direction: TradeDirection
  agreementHash: string
}

interface CreateTradeResult {
  createTrade: (params: CreateTradeParams) => Promise<void>
  isLoading: boolean
  error: string | null
  transactionHash?: string
  tradeId?: number
}

export function useCreateTrade(): CreateTradeResult {
  const [error, setError] = useState<string | null>(null)
  const [transactionHash, setTransactionHash] = useState<string>()
  const [tradeId, setTradeId] = useState<number>()
  
  const queryClient = useQueryClient()

  const { writeContract, data: hash, isPending: isWritePending } = useWriteContract()
  
  const { isLoading: isConfirming } = useWaitForTransactionReceipt({
    hash,
    onSuccess: (receipt) => {
      // Parse the TradeCreated event to get the trade ID
      const tradeCreatedEvent = receipt.logs.find(log => 
        log.topics[0] === '0x...' // TradeCreated event signature
      )
      
      if (tradeCreatedEvent) {
        // Extract trade ID from event data
        // This would need to be properly implemented based on the event structure
        console.log('Trade created successfully:', receipt)
        
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['trades'] })
        queryClient.invalidateQueries({ queryKey: ['trade-stats'] })
      }
    },
    onError: (error) => {
      setError(`Transaction failed: ${error.message}`)
    }
  })

  const prepareTradeMutation = useMutation({
    mutationFn: api.trades.prepareTrade,
    onError: (error: any) => {
      setError(`Failed to prepare trade: ${error.message}`)
    }
  })

  const createTrade = async (params: CreateTradeParams) => {
    try {
      setError(null)
      setTransactionHash(undefined)
      setTradeId(undefined)

      // First, prepare the trade on the backend to get parameters
      const tradeParams = await prepareTradeMutation.mutateAsync({
        maker_address: params.makerAddress,
        deposit_token_address: params.depositTokenAddress,
        price: params.price,
        deposit_amount: params.depositAmount,
        funding_window: params.fundingWindow,
        direction: params.direction,
        agreement_hash: params.agreementHash
      })

      // Convert string amounts to BigInt with proper decimals
      const priceWei = parseUnits(params.price, 18) // Assuming 18 decimals for price
      const depositAmountWei = parseUnits(params.depositAmount, 18) // This should use token decimals

      // Call the smart contract
      writeContract({
        address: OTC_HUB_ADDRESS,
        abi: OTC_HUB_ABI,
        functionName: 'createTrade',
        args: [
          params.makerAddress as `0x${string}`,
          params.depositTokenAddress as `0x${string}`,
          priceWei,
          depositAmountWei,
          BigInt(params.fundingWindow),
          params.direction,
          params.agreementHash as `0x${string}`
        ]
      })

      if (hash) {
        setTransactionHash(hash)
      }

    } catch (err: any) {
      setError(err.message || 'Failed to create trade')
    }
  }

  return {
    createTrade,
    isLoading: isWritePending || isConfirming || prepareTradeMutation.isPending,
    error,
    transactionHash,
    tradeId
  }
}
