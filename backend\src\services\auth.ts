import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { verifyMessage } from 'viem';
import { prisma } from '../utils/database';
import { logger } from '../utils/logger';
import { AuthChallenge, JwtPayload, AuthenticationError } from '../types';

export class AuthService {
  private static readonly CHALLENGE_EXPIRY_MINUTES = 10;
  private static readonly JWT_EXPIRY = '24h';

  /**
   * Generate a challenge message for wallet signature
   */
  public static async generateChallenge(address: string): Promise<AuthChallenge> {
    try {
      // Generate a unique nonce
      const nonce = crypto.randomBytes(32).toString('hex');
      
      // Create challenge message following EIP-4361 spirit
      const message = this.createChallengeMessage(address, nonce);
      
      // Set expiry time
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + this.CHALLENGE_EXPIRY_MINUTES);

      // Store challenge in database
      await prisma.authChallenge.create({
        data: {
          address: address.toLowerCase(),
          nonce,
          message,
          expiresAt,
        },
      });

      logger.info(`Generated challenge for address: ${address}`);

      return {
        message,
        nonce,
        expiresAt: expiresAt.toISOString(),
      };
    } catch (error) {
      logger.error('Failed to generate challenge:', error);
      throw new Error('Failed to generate authentication challenge');
    }
  }

  /**
   * Verify signature and issue JWT token
   */
  public static async verifySignatureAndIssueToken(
    address: string,
    signature: string,
    nonce: string
  ): Promise<string> {
    try {
      // Find the challenge
      const challenge = await prisma.authChallenge.findUnique({
        where: { nonce },
      });

      if (!challenge) {
        throw new AuthenticationError('Invalid or expired challenge');
      }

      // Check if challenge is expired
      if (new Date() > challenge.expiresAt) {
        throw new AuthenticationError('Challenge expired');
      }

      // Check if challenge is already used
      if (challenge.used) {
        throw new AuthenticationError('Challenge already used');
      }

      // Check if address matches
      if (challenge.address !== address.toLowerCase()) {
        throw new AuthenticationError('Address mismatch');
      }

      // Verify the signature
      const isValid = await verifyMessage({
        address: address as `0x${string}`,
        message: challenge.message,
        signature: signature as `0x${string}`,
      });

      if (!isValid) {
        throw new AuthenticationError('Invalid signature');
      }

      // Mark challenge as used
      await prisma.authChallenge.update({
        where: { nonce },
        data: { used: true },
      });

      // Generate JWT token
      const token = this.generateJWT(address);

      logger.info(`Authentication successful for address: ${address}`);

      return token;
    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error;
      }
      logger.error('Failed to verify signature:', error);
      throw new AuthenticationError('Authentication failed');
    }
  }

  /**
   * Verify JWT token
   */
  public static verifyToken(token: string): JwtPayload {
    try {
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        throw new Error('JWT_SECRET not configured');
      }

      const decoded = jwt.verify(token, jwtSecret) as JwtPayload;
      return decoded;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid token');
      }
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Token expired');
      }
      throw error;
    }
  }

  /**
   * Clean up expired challenges
   */
  public static async cleanupExpiredChallenges(): Promise<void> {
    try {
      const result = await prisma.authChallenge.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      if (result.count > 0) {
        logger.info(`Cleaned up ${result.count} expired challenges`);
      }
    } catch (error) {
      logger.error('Failed to cleanup expired challenges:', error);
    }
  }

  /**
   * Create challenge message following EIP-4361 format
   */
  private static createChallengeMessage(address: string, nonce: string): string {
    const domain = process.env.DOMAIN || 'localhost:3001';
    const uri = process.env.CORS_ORIGIN || 'http://localhost:5173';
    const version = '1';
    const chainId = process.env.CHAIN_ID || '31337';
    const issuedAt = new Date().toISOString();

    return `${domain} wants you to sign in with your Ethereum account:
${address}

Welcome to OTC Hub! Please sign this message to authenticate.

URI: ${uri}
Version: ${version}
Chain ID: ${chainId}
Nonce: ${nonce}
Issued At: ${issuedAt}`;
  }

  /**
   * Generate JWT token
   */
  private static generateJWT(address: string): string {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }

    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      address: address.toLowerCase(),
    };

    return jwt.sign(payload, jwtSecret, {
      expiresIn: this.JWT_EXPIRY,
    });
  }
}

// Setup periodic cleanup of expired challenges
setInterval(() => {
  AuthService.cleanupExpiredChallenges();
}, 60 * 60 * 1000); // Run every hour
