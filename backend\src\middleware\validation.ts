import { Request, Response, NextFunction } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { ValidationError } from '../types';

// Validation result handler
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ');
    throw new ValidationError(errorMessages);
  }
  next();
};

// Common validation rules
export const validateEthereumAddress = (field: string) => 
  body(field)
    .isLength({ min: 42, max: 42 })
    .matches(/^0x[a-fA-F0-9]{40}$/)
    .withMessage(`${field} must be a valid Ethereum address`);

export const validatePositiveNumber = (field: string) =>
  body(field)
    .isNumeric()
    .custom((value) => {
      if (parseFloat(value) <= 0) {
        throw new Error(`${field} must be a positive number`);
      }
      return true;
    });

export const validatePositiveInteger = (field: string) =>
  body(field)
    .isInt({ min: 1 })
    .withMessage(`${field} must be a positive integer`);

// Offer validation
export const validateCreateOffer = [
  body('direction')
    .isIn(['SELL', 'BUY'])
    .withMessage('Direction must be either SELL or BUY'),
  
  validateEthereumAddress('assetTokenAddress'),
  validateEthereumAddress('depositTokenAddress'),
  
  validatePositiveNumber('assetTotalQuantity'),
  validatePositiveNumber('price'),
  validatePositiveNumber('minTradeSize'),
  validatePositiveNumber('maxTradeSize'),
  validatePositiveNumber('depositAmount'),
  
  body('priceCurrency')
    .isLength({ min: 1, max: 10 })
    .withMessage('Price currency must be 1-10 characters'),
  
  validatePositiveInteger('fundingWindowSeconds'),
  
  body('minTradeSize')
    .custom((value, { req }) => {
      if (parseFloat(value) > parseFloat(req.body.maxTradeSize)) {
        throw new Error('minTradeSize cannot be greater than maxTradeSize');
      }
      return true;
    }),
  
  body('maxTradeSize')
    .custom((value, { req }) => {
      if (parseFloat(value) > parseFloat(req.body.assetTotalQuantity)) {
        throw new Error('maxTradeSize cannot be greater than assetTotalQuantity');
      }
      return true;
    }),
  
  handleValidationErrors,
];

// Trade validation
export const validateCreateTrade = [
  validatePositiveInteger('offerId'),
  validatePositiveNumber('quantity'),
  
  body('agreementHash')
    .isLength({ min: 66, max: 66 })
    .matches(/^0x[a-fA-F0-9]{64}$/)
    .withMessage('agreementHash must be a valid 32-byte hash'),
  
  handleValidationErrors,
];

// Auth validation
export const validateVerifySignature = [
  validateEthereumAddress('address'),
  
  body('signature')
    .isLength({ min: 132, max: 132 })
    .matches(/^0x[a-fA-F0-9]{130}$/)
    .withMessage('Signature must be a valid hex string'),
  
  body('nonce')
    .isLength({ min: 1 })
    .withMessage('Nonce is required'),
  
  handleValidationErrors,
];

// Query parameter validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  handleValidationErrors,
];

export const validateOfferFilters = [
  query('direction')
    .optional()
    .isIn(['SELL', 'BUY'])
    .withMessage('Direction must be either SELL or BUY'),
  
  query('assetTokenAddress')
    .optional()
    .matches(/^0x[a-fA-F0-9]{40}$/)
    .withMessage('assetTokenAddress must be a valid Ethereum address'),
  
  query('status')
    .optional()
    .isIn(['ACTIVE', 'INACTIVE', 'FILLED'])
    .withMessage('Status must be ACTIVE, INACTIVE, or FILLED'),
  
  handleValidationErrors,
];

export const validateTradeFilters = [
  query('status')
    .optional()
    .isIn(['Open', 'Funded', 'Settled', 'Cancelled', 'Disputed', 'AdminClosed'])
    .withMessage('Invalid trade status'),
  
  query('direction')
    .optional()
    .isIn(['MakerSells', 'MakerBuys'])
    .withMessage('Direction must be MakerSells or MakerBuys'),
  
  handleValidationErrors,
];
